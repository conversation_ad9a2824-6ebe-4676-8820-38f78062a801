#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试翻译前5行加油站名称
验证输出格式：目标名称（简体中文） | 目标名称（繁体中文） | 目标名称（英文）
"""

import pandas as pd
import requests
import time
import threading

class GeminiTranslator:
    """Gemini翻译器"""
    
    def __init__(self):
        self.api_key = "AIzaSyBBOTr5JN0eOUFf-GPkG3JIv_1Bvjhfz-8"
        self.base_url = "https://generativelanguage.googleapis.com"
        self.model = "gemini-2.0-flash"
        self.session = requests.Session()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful': 0,
            'failed': 0
        }
        self.stats_lock = threading.Lock()
    
    def translate_text(self, text, target_language):
        """使用Gemini翻译单个文本"""
        
        if pd.isna(text) or not str(text).strip():
            return None
        
        text = str(text).strip()
        
        # 构建翻译提示
        if target_language == 'zh-CN':
            prompt = f"请将以下台湾繁体中文加油站名称翻译为简体中文，只返回翻译结果，不要其他解释：{text}"
        elif target_language == 'en':
            prompt = f"请将以下中文加油站名称翻译为英文，只返回翻译结果，不要其他解释：{text}"
        else:
            return None
        
        try:
            url = f"{self.base_url}/v1beta/models/{self.model}:generateContent?key={self.api_key}"
            
            payload = {
                "contents": [{
                    "parts": [{
                        "text": prompt
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.1,
                    "maxOutputTokens": 100
                }
            }
            
            response = self.session.post(url, json=payload, timeout=15)
            
            with self.stats_lock:
                self.stats['total_requests'] += 1
            
            if response.status_code == 200:
                result = response.json()
                
                if 'candidates' in result and len(result['candidates']) > 0:
                    candidate = result['candidates'][0]
                    if 'content' in candidate and 'parts' in candidate['content']:
                        translated_text = candidate['content']['parts'][0]['text'].strip()
                        
                        with self.stats_lock:
                            self.stats['successful'] += 1
                        
                        return translated_text
                    else:
                        with self.stats_lock:
                            self.stats['failed'] += 1
                        print(f"    Gemini响应格式错误: {result}")
                        return None
                else:
                    with self.stats_lock:
                        self.stats['failed'] += 1
                    print(f"    Gemini无候选结果: {result}")
                    return None
            else:
                with self.stats_lock:
                    self.stats['failed'] += 1
                print(f"    Gemini API错误 {response.status_code}: {response.text}")
                return None
                
        except Exception as e:
            with self.stats_lock:
                self.stats['failed'] += 1
            print(f"    Gemini翻译异常: {e}")
            return None
    
    def translate_single_name(self, original_name, index):
        """翻译单个加油站名称"""
        
        print(f"🔍 第{index+1}行: {original_name}")
        
        # 翻译为简体中文
        print(f"  翻译为简体中文...")
        simplified_chinese = self.translate_text(original_name, 'zh-CN')
        if not simplified_chinese:
            simplified_chinese = original_name  # 使用原名作为备选
        
        time.sleep(2)  # 请求间隔
        
        # 翻译为英文
        print(f"  翻译为英文...")
        english = self.translate_text(original_name, 'en')
        if not english:
            english = original_name  # 使用原名作为备选
        
        time.sleep(2)  # 请求间隔
        
        print(f"✅ 第{index+1}行完成:")
        print(f"   简中: {simplified_chinese}")
        print(f"   繁中: {original_name}")
        print(f"   英文: {english}")
        
        return {
            '目标名称（简体中文）': simplified_chinese,
            '目标名称（繁体中文）': original_name,
            '目标名称（英文）': english
        }

def main():
    """主函数"""
    input_file = "加油站坐标距离分析_过滤后.csv"
    output_file = "测试翻译结果_前5行.csv"
    
    print("=== 测试翻译前5行加油站名称 ===")
    print(f"📁 输入文件: {input_file}")
    print(f"💾 输出文件: {output_file}")
    print("🤖 翻译服务: Gemini 2.0 Flash")
    print("📋 输出格式: 目标名称（简体中文） | 目标名称（繁体中文） | 目标名称（英文）")
    
    # 1. 加载数据
    try:
        df = pd.read_csv(input_file, encoding='utf-8-sig')
        print(f"\n📊 数据加载成功: {len(df)} 行")
        
        # 只取前5行
        first_5_names = []
        for i in range(min(5, len(df))):
            name = df.iloc[i, 0] if len(df.columns) > 0 else f'加油站{i+1}'
            first_5_names.append(str(name).strip())
        
        print(f"📍 前5个名称:")
        for i, name in enumerate(first_5_names, 1):
            print(f"  {i}. {name}")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 2. 翻译前5行
    print(f"\n🚀 开始翻译前5行...")
    translator = GeminiTranslator()
    results = []
    
    for i, name in enumerate(first_5_names):
        print(f"\n--- 处理第 {i+1}/5 行 ---")
        result = translator.translate_single_name(name, i)
        results.append(result)
    
    # 3. 保存结果
    print(f"\n💾 保存结果...")
    df_result = pd.DataFrame(results)
    df_result.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    # 4. 显示结果
    print(f"\n" + "="*60)
    print(f"🎉 测试完成！")
    print(f"📊 处理: {len(results)} 行")
    print(f"🌐 API请求: {translator.stats['total_requests']} 次")
    print(f"✅ 成功: {translator.stats['successful']} 次")
    print(f"❌ 失败: {translator.stats['failed']} 次")
    print(f"💾 结果已保存到: {output_file}")
    
    # 显示CSV内容预览
    print(f"\n📋 CSV文件内容预览:")
    print(df_result.to_string(index=False))
    
    print(f"\n📝 CSV文件格式验证:")
    print(f"列名: {list(df_result.columns)}")
    print(f"行数: {len(df_result)}")
    print(f"列数: {len(df_result.columns)}")

if __name__ == "__main__":
    main()
