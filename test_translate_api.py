#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Google翻译API
"""

import requests

def test_translate_api():
    """测试翻译API"""
    
    api_key = "AIzaSyBTPmacyTusdVvhu3Y5fMyl0skocYKA5u0"
    
    # 测试文本
    test_text = "汀州路加油站"
    
    print(f"🔍 测试翻译API")
    print(f"📝 测试文本: {test_text}")
    print(f"🔑 API密钥: {api_key[:20]}...")
    
    # 测试翻译为简体中文
    print(f"\n--- 测试翻译为简体中文 ---")
    try:
        url = f"https://translation.googleapis.com/language/translate/v2?key={api_key}"
        
        data = {
            'q': test_text,
            'target': 'zh-CN',
            'source': 'auto',
            'format': 'text'
        }
        
        response = requests.post(url, data=data, timeout=10)
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if 'data' in result and 'translations' in result['data']:
                translated = result['data']['translations'][0]['translatedText']
                print(f"✅ 翻译成功: {translated}")
            else:
                print(f"❌ 响应格式错误")
        else:
            print(f"❌ API调用失败")
            
    except Exception as e:
        print(f"❌ 异常: {e}")
    
    # 测试翻译为英文
    print(f"\n--- 测试翻译为英文 ---")
    try:
        url = f"https://translation.googleapis.com/language/translate/v2?key={api_key}"
        
        data = {
            'q': test_text,
            'target': 'en',
            'source': 'auto',
            'format': 'text'
        }
        
        response = requests.post(url, data=data, timeout=10)
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if 'data' in result and 'translations' in result['data']:
                translated = result['data']['translations'][0]['translatedText']
                print(f"✅ 翻译成功: {translated}")
            else:
                print(f"❌ 响应格式错误")
        else:
            print(f"❌ API调用失败")
            
    except Exception as e:
        print(f"❌ 异常: {e}")

if __name__ == "__main__":
    test_translate_api()
