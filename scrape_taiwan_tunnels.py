#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台湾隧道数据爬取脚本
从维基百科页面提取所有隧道表格数据并保存为Excel文件
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import re
from urllib.parse import urljoin
import time

def get_page_content(url):
    """获取页面内容"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'
        return response.text
    except requests.RequestException as e:
        print(f"获取页面失败: {e}")
        return None

def clean_text(text):
    """清理文本内容"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    # 移除引用标记
    text = re.sub(r'\[\d+\]', '', text)
    # 移除特殊字符
    text = re.sub(r'[​‌‍]', '', text)
    
    return text

def extract_table_data(soup, table_title):
    """提取表格数据"""
    tables_data = []
    
    # 查找所有表格
    tables = soup.find_all('table', class_='wikitable')
    
    for i, table in enumerate(tables):
        print(f"处理表格 {i+1}/{len(tables)}: {table_title}")
        
        # 提取表头
        headers = []
        header_row = table.find('tr')
        if header_row:
            for th in header_row.find_all(['th', 'td']):
                header_text = clean_text(th.get_text())
                headers.append(header_text)
        
        # 如果没有找到表头，跳过
        if not headers:
            continue
            
        # 提取数据行
        rows_data = []
        for row in table.find_all('tr')[1:]:  # 跳过表头行
            cells = row.find_all(['td', 'th'])
            if len(cells) >= len(headers):
                row_data = []
                for j, cell in enumerate(cells[:len(headers)]):
                    cell_text = clean_text(cell.get_text())
                    row_data.append(cell_text)
                rows_data.append(row_data)
        
        if rows_data:
            # 创建DataFrame
            df = pd.DataFrame(rows_data, columns=headers)
            tables_data.append({
                'title': f"{table_title}_表格{i+1}",
                'data': df
            })
    
    return tables_data

def extract_all_tables(soup):
    """提取所有表格数据，更精确的方法"""
    all_tables = []

    # 查找所有表格
    tables = soup.find_all('table')

    print(f"找到 {len(tables)} 个表格")

    for i, table in enumerate(tables):
        # 检查是否是数据表格（有多行数据）
        rows = table.find_all('tr')
        if len(rows) < 2:  # 至少要有表头和一行数据
            continue

        # 尝试找到表格所属的章节
        section_title = f"表格{i+1}"

        # 向上查找最近的标题
        current = table.parent
        search_depth = 0
        while current and search_depth < 10:
            # 查找前面的标题元素
            prev_heading = current.find_previous(['h1', 'h2', 'h3', 'h4', 'h5'])
            if prev_heading:
                heading_text = clean_text(prev_heading.get_text())
                if heading_text and len(heading_text) < 50:  # 避免过长的文本
                    section_title = heading_text
                    break
            current = current.parent
            search_depth += 1

        print(f"处理表格 {i+1}: {section_title}")

        # 提取表头 - 更智能的表头检测
        headers = []
        header_candidates = []

        # 检查前几行，找到最合适的表头
        for row_idx, row in enumerate(rows[:3]):  # 只检查前3行
            cells = row.find_all(['th', 'td'])
            if cells:
                row_headers = []
                for cell in cells:
                    cell_text = clean_text(cell.get_text())
                    row_headers.append(cell_text)

                if row_headers and any(h for h in row_headers):  # 至少有一个非空表头
                    header_candidates.append((row_idx, row_headers))

        # 选择最好的表头
        if header_candidates:
            # 优先选择th标签的行，或者第一行
            best_header = header_candidates[0]
            for row_idx, candidate_headers in header_candidates:
                row = rows[row_idx]
                if row.find('th'):  # 如果有th标签，优先选择
                    best_header = (row_idx, candidate_headers)
                    break

            headers = best_header[1]
            data_start_row = best_header[0] + 1
        else:
            continue

        # 清理表头
        cleaned_headers = []
        for h in headers:
            if h:
                cleaned_headers.append(h)
            else:
                cleaned_headers.append(f"列{len(cleaned_headers)+1}")
        headers = cleaned_headers

        if not headers:
            continue

        print(f"  表头: {headers}")

        # 提取数据行
        rows_data = []
        for row in rows[data_start_row:]:
            cells = row.find_all(['td', 'th'])
            if cells:
                row_data = []
                for cell in cells:
                    cell_text = clean_text(cell.get_text())
                    row_data.append(cell_text)

                # 只保留有效数据行（至少有一个非空单元格）
                if any(cell.strip() for cell in row_data):
                    # 调整行数据长度与表头一致
                    while len(row_data) < len(headers):
                        row_data.append("")
                    rows_data.append(row_data[:len(headers)])

        if rows_data:
            df = pd.DataFrame(rows_data, columns=headers)
            # 过滤掉完全空白的行
            df = df.dropna(how='all')
            df = df[~(df == '').all(axis=1)]

            if not df.empty:
                all_tables.append({
                    'title': section_title,
                    'data': df,
                    'row_count': len(df),
                    'col_count': len(df.columns)
                })
                print(f"  提取到 {len(df)} 行 {len(df.columns)} 列数据")

    return all_tables

def save_to_multiple_formats(all_tables):
    """保存为多种格式"""
    if not all_tables:
        print("没有数据可保存")
        return

    # 保存为Excel
    excel_file = "台湾隧道数据.xlsx"
    with pd.ExcelWriter(excel_file, engine='openpyxl') as writer:
        for i, table_info in enumerate(all_tables):
            title = table_info['title']
            df = table_info['data']

            # Excel工作表名称限制和清理
            sheet_name = title[:31] if len(title) > 31 else title
            sheet_name = re.sub(r'[\\/*?:"<>|]', '_', sheet_name)
            sheet_name = f"表{i+1}_{sheet_name}" if sheet_name.isdigit() else sheet_name

            print(f"保存工作表: {sheet_name} ({len(df)} 行数据)")
            df.to_excel(writer, sheet_name=sheet_name, index=False)

    print(f"Excel文件已保存: {excel_file}")

    # 保存为CSV文件（每个表格一个文件）
    import os
    csv_dir = "台湾隧道数据_CSV"
    os.makedirs(csv_dir, exist_ok=True)

    for i, table_info in enumerate(all_tables):
        title = table_info['title']
        df = table_info['data']

        # 清理文件名
        filename = re.sub(r'[\\/*?:"<>|]', '_', title)
        csv_file = os.path.join(csv_dir, f"表{i+1}_{filename}.csv")

        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        print(f"CSV文件已保存: {csv_file}")

def main():
    """主函数"""
    url = "https://zh.wikipedia.org/wiki/%E5%8F%B0%E7%81%A3%E9%9A%A7%E9%81%93"

    print("开始爬取台湾隧道数据...")
    print(f"目标URL: {url}")

    # 获取页面内容
    html_content = get_page_content(url)
    if not html_content:
        print("无法获取页面内容")
        return

    # 解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')

    # 提取所有表格
    all_tables = extract_all_tables(soup)

    if not all_tables:
        print("未找到任何表格数据")
        return

    print(f"\n共找到 {len(all_tables)} 个有效表格")

    # 保存数据
    save_to_multiple_formats(all_tables)

    # 显示详细统计信息
    total_rows = sum(table['row_count'] for table in all_tables)
    print(f"\n=== 统计信息 ===")
    print(f"总表格数: {len(all_tables)}")
    print(f"总数据行数: {total_rows}")
    print(f"\n各表格详情:")

    for i, table_info in enumerate(all_tables, 1):
        title = table_info['title']
        row_count = table_info['row_count']
        col_count = table_info['col_count']
        print(f"{i:2d}. {title}: {row_count} 行 × {col_count} 列")

    print(f"\n数据提取完成！")
    print(f"Excel文件: 台湾隧道数据.xlsx")
    print(f"CSV文件夹: 台湾隧道数据_CSV/")

if __name__ == "__main__":
    main()
