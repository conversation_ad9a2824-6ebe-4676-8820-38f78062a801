#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
台湾隧道数据爬取脚本
从维基百科页面提取所有隧道表格数据并保存为Excel文件
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import re
from urllib.parse import urljoin
import time

def get_page_content(url):
    """获取页面内容"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'
        return response.text
    except requests.RequestException as e:
        print(f"获取页面失败: {e}")
        return None

def clean_text(text):
    """清理文本内容"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    # 移除引用标记
    text = re.sub(r'\[\d+\]', '', text)
    # 移除特殊字符
    text = re.sub(r'[​‌‍]', '', text)
    
    return text

def extract_table_data(soup, table_title):
    """提取表格数据"""
    tables_data = []
    
    # 查找所有表格
    tables = soup.find_all('table', class_='wikitable')
    
    for i, table in enumerate(tables):
        print(f"处理表格 {i+1}/{len(tables)}: {table_title}")
        
        # 提取表头
        headers = []
        header_row = table.find('tr')
        if header_row:
            for th in header_row.find_all(['th', 'td']):
                header_text = clean_text(th.get_text())
                headers.append(header_text)
        
        # 如果没有找到表头，跳过
        if not headers:
            continue
            
        # 提取数据行
        rows_data = []
        for row in table.find_all('tr')[1:]:  # 跳过表头行
            cells = row.find_all(['td', 'th'])
            if len(cells) >= len(headers):
                row_data = []
                for j, cell in enumerate(cells[:len(headers)]):
                    cell_text = clean_text(cell.get_text())
                    row_data.append(cell_text)
                rows_data.append(row_data)
        
        if rows_data:
            # 创建DataFrame
            df = pd.DataFrame(rows_data, columns=headers)
            tables_data.append({
                'title': f"{table_title}_表格{i+1}",
                'data': df
            })
    
    return tables_data

def extract_section_tables(soup):
    """按章节提取表格"""
    all_tables = []
    
    # 定义要提取的章节
    sections = {
        '台鐵': '台铁隧道',
        '台灣高鐵': '台湾高铁隧道', 
        '阿里山林業鐵路': '阿里山林业铁路隧道',
        '臺北捷運': '台北捷运隧道',
        '新北捷運': '新北捷运隧道',
        '長公路隧道': '长公路隧道',
        '長度在1公里以上的隧道': '1公里以上隧道',
        '臺北市': '台北市隧道',
        '新北市': '新北市隧道',
        '基隆市': '基隆市隧道',
        '桃園市': '桃园市隧道',
        '新竹市': '新竹市隧道',
        '新竹縣': '新竹县隧道',
        '苗栗縣': '苗栗县隧道',
        '台中市': '台中市隧道',
        '彰化縣': '彰化县隧道',
        '南投縣': '南投县隧道',
        '雲林縣': '云林县隧道',
        '嘉義市': '嘉义市隧道',
        '嘉義縣': '嘉义县隧道',
        '台南市': '台南市隧道',
        '高雄市': '高雄市隧道',
        '屏東縣': '屏东县隧道',
        '宜蘭縣': '宜兰县隧道',
        '花蓮縣': '花莲县隧道',
        '台東縣': '台东县隧道',
        '自行車專用道': '自行车专用道隧道',
        '行人專用道': '行人专用道隧道',
        '水利': '水利隧道'
    }
    
    # 查找所有表格
    tables = soup.find_all('table', class_='wikitable')
    
    for i, table in enumerate(tables):
        # 尝试找到表格所属的章节
        section_title = f"表格{i+1}"
        
        # 向上查找最近的标题
        current = table
        while current:
            current = current.find_previous(['h2', 'h3', 'h4'])
            if current:
                title_text = clean_text(current.get_text())
                for section_key, section_name in sections.items():
                    if section_key in title_text:
                        section_title = section_name
                        break
                if section_title != f"表格{i+1}":
                    break
        
        print(f"处理 {section_title} 的表格...")
        
        # 提取表头
        headers = []
        header_row = table.find('tr')
        if header_row:
            for th in header_row.find_all(['th', 'td']):
                header_text = clean_text(th.get_text())
                if header_text:
                    headers.append(header_text)
        
        if not headers:
            continue
            
        # 提取数据行
        rows_data = []
        for row in table.find_all('tr')[1:]:
            cells = row.find_all(['td', 'th'])
            if cells:
                row_data = []
                for cell in cells:
                    cell_text = clean_text(cell.get_text())
                    row_data.append(cell_text)
                
                # 确保行数据长度与表头一致
                while len(row_data) < len(headers):
                    row_data.append("")
                
                rows_data.append(row_data[:len(headers)])
        
        if rows_data:
            df = pd.DataFrame(rows_data, columns=headers)
            all_tables.append({
                'title': section_title,
                'data': df
            })
    
    return all_tables

def main():
    """主函数"""
    url = "https://zh.wikipedia.org/wiki/%E5%8F%B0%E7%81%A3%E9%9A%A7%E9%81%93"
    
    print("开始爬取台湾隧道数据...")
    print(f"目标URL: {url}")
    
    # 获取页面内容
    html_content = get_page_content(url)
    if not html_content:
        print("无法获取页面内容")
        return
    
    # 解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 提取所有表格
    all_tables = extract_section_tables(soup)
    
    if not all_tables:
        print("未找到任何表格数据")
        return
    
    print(f"共找到 {len(all_tables)} 个表格")
    
    # 保存到Excel文件
    output_file = "台湾隧道数据.xlsx"
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        for table_info in all_tables:
            title = table_info['title']
            df = table_info['data']
            
            # Excel工作表名称限制
            sheet_name = title[:31] if len(title) > 31 else title
            sheet_name = re.sub(r'[\\/*?:"<>|]', '_', sheet_name)
            
            print(f"保存工作表: {sheet_name} ({len(df)} 行数据)")
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"数据已保存到: {output_file}")
    
    # 显示统计信息
    total_rows = sum(len(table['data']) for table in all_tables)
    print(f"\n统计信息:")
    print(f"- 总表格数: {len(all_tables)}")
    print(f"- 总数据行数: {total_rows}")
    
    for table_info in all_tables:
        title = table_info['title']
        df = table_info['data']
        print(f"- {title}: {len(df)} 行, {len(df.columns)} 列")

if __name__ == "__main__":
    main()
