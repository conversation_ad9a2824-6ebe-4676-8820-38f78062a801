#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
30并发搜索台湾隧道坐标
自动为每个点位名称添加"台湾"前缀
"""

import pandas as pd
import requests
import time
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

def dms_to_decimal(degrees, minutes, seconds):
    """将度分秒转换为十进制度数"""
    return degrees + minutes/60 + seconds/3600

class CoordinateCorrector:
    """坐标校正器 - 并发版本"""
    
    def __init__(self):
        self.google_api_key = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"
        self.request_lock = threading.Lock()
        
        # 台湾的地理范围
        self.taiwan_bounds = {
            'min_lat': 21.5, 'max_lat': 25.5,
            'min_lng': 119.5, 'max_lng': 122.5
        }
    
    def is_in_taiwan(self, lat, lng):
        """检查坐标是否在台湾范围内"""
        return (self.taiwan_bounds['min_lat'] <= lat <= self.taiwan_bounds['max_lat'] and
                self.taiwan_bounds['min_lng'] <= lng <= self.taiwan_bounds['max_lng'])
    
    def search_coordinates(self, name):
        """搜索单个名称的坐标"""
        
        # 构建搜索查询 - 优先使用台湾前缀
        queries = [
            f"台湾 {name}",
            f"{name} 台湾",
            f"{name} Taiwan",
            f"台湾省 {name}",
            f"中华民国 {name}"
        ]
        
        for query in queries:
            try:
                url = "https://maps.googleapis.com/maps/api/geocode/json"
                params = {
                    'address': query,
                    'key': self.google_api_key,
                    'region': 'tw',
                    'language': 'zh-TW'
                }
                
                # 使用锁控制API请求频率
                with self.request_lock:
                    response = requests.get(url, params=params, timeout=15)
                    time.sleep(0.03)  # 短暂延迟避免API限制
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data['status'] == 'OK' and data['results']:
                        result = data['results'][0]
                        location = result['geometry']['location']
                        lat, lng = location['lat'], location['lng']
                        
                        # 验证是否在台湾范围内
                        if self.is_in_taiwan(lat, lng):
                            confidence = 'HIGH' if any(keyword in result['formatted_address'] 
                                                     for keyword in ['台湾', 'Taiwan', '台灣']) else 'MEDIUM'
                            
                            return {
                                'lng': lng,
                                'lat': lat,
                                'source': 'Google Geocoding API',
                                'confidence': confidence,
                                'formatted_address': result['formatted_address'],
                                'query_used': query,
                                'success': True
                            }
                
            except Exception as e:
                continue  # 尝试下一个查询
        
        return {'success': False, 'error': '未找到有效坐标'}

def extract_names_from_excel(excel_file):
    """从Excel文件提取第一列名称"""
    all_names = set()
    
    try:
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        for sheet_name, df in excel_data.items():
            if df.empty or len(df.columns) == 0:
                continue
            
            first_column = df.iloc[:, 0]
            for value in first_column:
                if pd.notna(value) and str(value).strip():
                    name = str(value).strip()
                    name = re.sub(r'^\d+\.?\s*', '', name)
                    name = re.sub(r'\s+', ' ', name)
                    
                    if len(name) > 2:
                        all_names.add(name)
    
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return []
    
    return sorted(list(all_names))

def search_single_name(corrector, name, index, total):
    """搜索单个名称的坐标"""
    try:
        print(f"[{index:3d}/{total}] 🔍 {name}")
        
        result = corrector.search_coordinates(name)
        
        if result['success']:
            data = {
                '序号': index,
                '名称': name,
                '经度': result['lng'],
                '纬度': result['lat'],
                '数据源': result['source'],
                '置信度': result['confidence'],
                '格式化地址': result['formatted_address'],
                '搜索查询': result['query_used'],
                '状态': '成功'
            }
            print(f"[{index:3d}/{total}] ✅ {name}: ({result['lat']:.6f}, {result['lng']:.6f})")
        else:
            data = {
                '序号': index,
                '名称': name,
                '经度': None,
                '纬度': None,
                '数据源': 'N/A',
                '置信度': 'FAILED',
                '格式化地址': '',
                '搜索查询': '',
                '状态': '未找到'
            }
            print(f"[{index:3d}/{total}] ❌ {name}: 未找到")
        
        return data
        
    except Exception as e:
        print(f"[{index:3d}/{total}] ❌ {name}: 错误 - {e}")
        return {
            '序号': index,
            '名称': name,
            '经度': None,
            '纬度': None,
            '数据源': 'ERROR',
            '置信度': 'ERROR',
            '格式化地址': str(e),
            '搜索查询': '',
            '状态': '错误'
        }

def concurrent_search(names_list, max_workers=30):
    """30并发搜索坐标"""
    
    print(f"🚀 开始30并发搜索 {len(names_list)} 个点位坐标")
    print(f"📍 每个名称都会自动添加'台湾'前缀搜索")
    print(f"⚡ 并发线程数: {max_workers}")
    print("-" * 60)
    
    corrector = CoordinateCorrector()
    results = []
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有搜索任务
        future_to_data = {
            executor.submit(search_single_name, corrector, name, i, len(names_list)): (i, name)
            for i, name in enumerate(names_list, 1)
        }
        
        # 收集结果
        completed = 0
        for future in as_completed(future_to_data):
            result = future.result()
            results.append(result)
            completed += 1
            
            # 显示进度
            if completed % 20 == 0 or completed == len(names_list):
                elapsed = time.time() - start_time
                rate = completed / elapsed if elapsed > 0 else 0
                print(f"📊 进度: {completed}/{len(names_list)} ({completed/len(names_list)*100:.1f}%) - {rate:.1f} 个/秒")
    
    # 按序号排序
    results.sort(key=lambda x: x['序号'])
    
    return results

def main():
    """主函数"""
    excel_file = "台湾隧道特定表格.xlsx"
    output_file = "台湾隧道坐标_30并发搜索.xlsx"
    
    print("=== 台湾隧道坐标30并发搜索 ===")
    print(f"📁 输入文件: {excel_file}")
    print(f"💾 输出文件: {output_file}")
    
    # 1. 提取名称
    print("\n步骤1: 提取表格第一列名称...")
    names = extract_names_from_excel(excel_file)
    
    if not names:
        print("❌ 未找到任何有效名称")
        return
    
    print(f"✅ 提取到 {len(names)} 个唯一名称")
    
    # 显示前10个名称
    print("\n📋 前10个名称预览:")
    for i, name in enumerate(names[:10], 1):
        print(f"  {i:2d}. {name}")
    if len(names) > 10:
        print(f"     ... 还有 {len(names)-10} 个")
    
    # 2. 开始并发搜索
    print(f"\n步骤2: 30并发搜索坐标...")
    start_time = time.time()
    
    results = concurrent_search(names, max_workers=30)
    
    # 3. 保存结果
    df = pd.DataFrame(results)
    df.to_excel(output_file, index=False)
    
    # 4. 统计结果
    elapsed_time = time.time() - start_time
    success_count = len(df[df['状态'] == '成功'])
    failed_count = len(df[df['状态'] == '未找到'])
    error_count = len(df[df['状态'] == '错误'])
    
    print(f"\n" + "="*60)
    print(f"🎉 搜索完成！")
    print(f"⏱️  总耗时: {elapsed_time:.1f} 秒")
    print(f"📊 总计: {len(results)} 个名称")
    print(f"✅ 成功: {success_count} 个 ({success_count/len(results)*100:.1f}%)")
    print(f"❌ 未找到: {failed_count} 个")
    print(f"⚠️  错误: {error_count} 个")
    print(f"🚀 平均速度: {len(results)/elapsed_time:.1f} 个/秒")
    print(f"💾 结果已保存到: {output_file}")

if __name__ == "__main__":
    main()
