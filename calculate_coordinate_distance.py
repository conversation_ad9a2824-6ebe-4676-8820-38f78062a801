#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计算加油站坐标搜索结果_完整版.csv中两组坐标之间的距离
第2、3列坐标 vs 第4、5列坐标，单位：米
"""

import pandas as pd
import math
import numpy as np

def haversine_distance(lat1, lon1, lat2, lon2):
    """
    使用Haversine公式计算两个坐标点之间的距离（米）
    
    参数:
    lat1, lon1: 第一个点的纬度和经度
    lat2, lon2: 第二个点的纬度和经度
    
    返回:
    距离（米）
    """
    # 检查输入是否有效
    if pd.isna(lat1) or pd.isna(lon1) or pd.isna(lat2) or pd.isna(lon2):
        return None
    
    # 地球半径（米）
    R = 6371000
    
    # 将度数转换为弧度
    lat1_rad = math.radians(float(lat1))
    lon1_rad = math.radians(float(lon1))
    lat2_rad = math.radians(float(lat2))
    lon2_rad = math.radians(float(lon2))
    
    # 计算差值
    dlat = lat2_rad - lat1_rad
    dlon = lon2_rad - lon1_rad
    
    # Haversine公式
    a = math.sin(dlat/2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon/2)**2
    c = 2 * math.asin(math.sqrt(a))
    
    # 计算距离
    distance = R * c
    
    return distance

def calculate_distances(csv_file):
    """计算CSV文件中两组坐标的距离"""
    
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        print(f"📊 数据信息:")
        print(f"  行数: {len(df)}")
        print(f"  列数: {len(df.columns)}")
        print(f"  列名: {list(df.columns)}")
        
        # 检查列数
        if len(df.columns) < 5:
            print(f"❌ 列数不足，需要至少5列，实际只有{len(df.columns)}列")
            return None
        
        # 获取坐标列
        # 假设列顺序：名称, 经度, 纬度, 地址经度, 地址纬度
        coord1_lng_col = df.columns[1]  # 第2列：经度
        coord1_lat_col = df.columns[2]  # 第3列：纬度
        coord2_lng_col = df.columns[3]  # 第4列：地址经度
        coord2_lat_col = df.columns[4]  # 第5列：地址纬度
        
        print(f"\n📍 坐标列信息:")
        print(f"  第一组坐标: {coord1_lat_col} ({coord1_lng_col})")
        print(f"  第二组坐标: {coord2_lat_col} ({coord2_lng_col})")
        
        # 计算距离
        distances = []
        valid_count = 0
        invalid_count = 0
        
        print(f"\n🔄 开始计算距离...")
        
        for idx, row in df.iterrows():
            # 获取两组坐标
            lat1 = row[coord1_lat_col]
            lng1 = row[coord1_lng_col]
            lat2 = row[coord2_lat_col]
            lng2 = row[coord2_lng_col]
            
            # 计算距离
            distance = haversine_distance(lat1, lng1, lat2, lng2)
            
            if distance is not None:
                distances.append(distance)
                valid_count += 1
                
                # 显示进度（每100行显示一次）
                if (idx + 1) % 100 == 0:
                    print(f"  处理进度: {idx + 1}/{len(df)} ({(idx + 1)/len(df)*100:.1f}%)")
            else:
                distances.append(None)
                invalid_count += 1
        
        # 添加距离列到DataFrame
        df['距离_米'] = distances
        
        print(f"✅ 距离计算完成")
        print(f"  有效计算: {valid_count} 个")
        print(f"  无效数据: {invalid_count} 个")
        
        return df
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return None

def analyze_distances(df):
    """分析距离数据"""
    
    # 获取有效距离数据
    valid_distances = df['距离_米'].dropna()
    
    if len(valid_distances) == 0:
        print("❌ 没有有效的距离数据")
        return
    
    print(f"\n📊 距离统计分析:")
    print(f"  有效数据量: {len(valid_distances)}")
    print(f"  最小距离: {valid_distances.min():.2f} 米")
    print(f"  最大距离: {valid_distances.max():.2f} 米")
    print(f"  平均距离: {valid_distances.mean():.2f} 米")
    print(f"  中位数距离: {valid_distances.median():.2f} 米")
    print(f"  标准差: {valid_distances.std():.2f} 米")
    
    # 距离分布统计
    print(f"\n📈 距离分布:")
    ranges = [
        (0, 100, "0-100米"),
        (100, 500, "100-500米"),
        (500, 1000, "500米-1公里"),
        (1000, 5000, "1-5公里"),
        (5000, 10000, "5-10公里"),
        (10000, float('inf'), "10公里以上")
    ]
    
    for min_dist, max_dist, label in ranges:
        count = len(valid_distances[(valid_distances >= min_dist) & (valid_distances < max_dist)])
        percentage = count / len(valid_distances) * 100
        print(f"  {label}: {count} 个 ({percentage:.1f}%)")
    
    # 显示距离最小和最大的案例
    print(f"\n🎯 距离最小的5个案例:")
    closest = df.nsmallest(5, '距离_米')
    for i, (idx, row) in enumerate(closest.iterrows(), 1):
        name = row.iloc[0]  # 第一列是名称
        distance = row['距离_米']
        print(f"  {i}. {name}: {distance:.2f} 米")
    
    print(f"\n📏 距离最大的5个案例:")
    farthest = df.nlargest(5, '距离_米')
    for i, (idx, row) in enumerate(farthest.iterrows(), 1):
        name = row.iloc[0]  # 第一列是名称
        distance = row['距离_米']
        print(f"  {i}. {name}: {distance:.2f} 米")

def main():
    """主函数"""
    input_file = "加油站坐标搜索结果_完整版.csv"
    output_file = "加油站坐标距离分析.csv"
    
    print("=== 加油站坐标距离计算程序 ===")
    print(f"📁 输入文件: {input_file}")
    print(f"💾 输出文件: {output_file}")
    print("🔍 计算: 第2、3列坐标 vs 第4、5列坐标的距离")
    
    # 1. 计算距离
    print(f"\n步骤1: 加载数据并计算距离...")
    df_with_distances = calculate_distances(input_file)
    
    if df_with_distances is None:
        print("❌ 距离计算失败")
        return
    
    # 2. 分析距离数据
    print(f"\n步骤2: 分析距离数据...")
    analyze_distances(df_with_distances)
    
    # 3. 保存结果
    print(f"\n步骤3: 保存分析结果...")
    df_with_distances.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"\n" + "="*60)
    print(f"🎉 距离计算完成！")
    print(f"💾 结果已保存到: {output_file}")
    print(f"📋 新增列: 距离_米")
    
    # 显示CSV格式预览
    print(f"\n📋 输出文件格式预览:")
    print(f"名称,经度,纬度,地址经度,地址纬度,距离_米")
    
    # 显示前3行有效数据作为示例
    valid_rows = df_with_distances[df_with_distances['距离_米'].notna()].head(3)
    for idx, row in valid_rows.iterrows():
        name = row.iloc[0]
        lng1 = row.iloc[1] if pd.notna(row.iloc[1]) else ''
        lat1 = row.iloc[2] if pd.notna(row.iloc[2]) else ''
        lng2 = row.iloc[3] if pd.notna(row.iloc[3]) else ''
        lat2 = row.iloc[4] if pd.notna(row.iloc[4]) else ''
        distance = row['距离_米']
        print(f"{name},{lng1},{lat1},{lng2},{lat2},{distance:.2f}")
    
    print(f"\n📝 说明:")
    print(f"- 使用Haversine公式计算球面距离")
    print(f"- 距离单位：米")
    print(f"- 缺少坐标的行距离为空")
    print(f"- 可用于评估两种搜索方法的准确性")

if __name__ == "__main__":
    main()
