#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用CoordinateCorrector类搜索表格第一列名称的精确坐标
"""

import pandas as pd
import requests
import time
import json
import re
from typing import Dict, List, Tuple, Optional

def dms_to_decimal(degrees, minutes, seconds):
    """将度分秒转换为十进制度数"""
    return degrees + minutes/60 + seconds/3600

class CoordinateCorrector:
    """坐标校正器"""
    
    def __init__(self):
        self.google_api_key = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"
        
        # 已知的正确坐标（手动校正）
        self.manual_corrections = {
            '屏東縣議會': {
                'lat': dms_to_decimal(22, 40, 54),  # 22°40'54"N
                'lng': dms_to_decimal(120, 29, 18), # 120°29'18"E
                'source': '手动校正',
                'confidence': 'HIGH'
            },
            # 可以继续添加其他已知的正确坐标
        }
        
        # 台湾的地理范围
        self.taiwan_bounds = {
            'min_lat': 21.5, 'max_lat': 25.5,
            'min_lng': 119.5, 'max_lng': 122.5
        }
    
    def is_in_taiwan(self, lat, lng):
        """检查坐标是否在台湾范围内"""
        return (self.taiwan_bounds['min_lat'] <= lat <= self.taiwan_bounds['max_lat'] and
                self.taiwan_bounds['min_lng'] <= lng <= self.taiwan_bounds['max_lng'])
    
    def google_geocode_with_validation(self, name, address=""):
        """使用Google Geocoding API获取坐标并验证"""
        
        # 构建搜索查询
        queries = []
        if address:
            queries.append(f"{name} {address}")
        queries.append(f"{name} 台湾")
        queries.append(f"{name} Taiwan")
        
        for query in queries:
            try:
                url = "https://maps.googleapis.com/maps/api/geocode/json"
                params = {
                    'address': query,
                    'key': self.google_api_key,
                    'region': 'tw',  # 偏向台湾结果
                    'language': 'zh-TW'
                }
                
                response = requests.get(url, params=params, timeout=10)
                response.raise_for_status()
                data = response.json()
                
                if data['status'] == 'OK' and data['results']:
                    result = data['results'][0]
                    location = result['geometry']['location']
                    lat, lng = location['lat'], location['lng']
                    
                    # 验证是否在台湾范围内
                    if self.is_in_taiwan(lat, lng):
                        confidence = 'HIGH' if '台湾' in result['formatted_address'] or 'Taiwan' in result['formatted_address'] else 'MEDIUM'
                        
                        return {
                            'lng': lng,
                            'lat': lat,
                            'source': 'Google Geocoding API',
                            'confidence': confidence,
                            'formatted_address': result['formatted_address'],
                            'query_used': query
                        }
                
                # API限制，稍作延迟
                time.sleep(0.1)
                
            except Exception as e:
                print(f"    ❌ 查询失败 '{query}': {e}")
                continue
        
        return None
    
    def get_corrected_coordinates(self, institution_name, address=""):
        """获取校正后的坐标，返回包含置信度的结果"""
        
        # 1. 首先检查是否有手动校正的坐标
        if institution_name in self.manual_corrections:
            correction = self.manual_corrections[institution_name]
            print(f"  🔧 使用手动校正坐标")
            return {
                'lng': correction['lng'],
                'lat': correction['lat'],
                'source': correction['source'],
                'confidence': correction['confidence'],
                'formatted_address': address or '手动校正地址'
            }
        
        # 2. 使用机构名称进行Google API搜索
        result = self.google_geocode_with_validation(institution_name, address)
        return result

def extract_first_column_names(excel_file):
    """提取Excel文件中所有工作表第一列的名称"""
    all_names = set()  # 使用set避免重复
    
    try:
        # 读取Excel文件的所有工作表
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        for sheet_name, df in excel_data.items():
            print(f"处理工作表: {sheet_name}")
            
            if df.empty or len(df.columns) == 0:
                print(f"  工作表 {sheet_name} 为空，跳过")
                continue
            
            # 获取第一列的所有值
            first_column = df.iloc[:, 0]
            
            for value in first_column:
                if pd.notna(value) and str(value).strip():
                    # 清理名称
                    name = str(value).strip()
                    # 移除可能的编号或特殊字符
                    name = re.sub(r'^\d+\.?\s*', '', name)  # 移除开头的数字
                    name = re.sub(r'\s+', ' ', name)  # 规范化空格
                    
                    if len(name) > 2:  # 只保留有意义的名称
                        all_names.add(name)
            
            print(f"  从 {sheet_name} 提取到 {len(first_column.dropna())} 个名称")
    
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return []
    
    return sorted(list(all_names))

def search_coordinates_for_names(names_list, output_file="坐标搜索结果.xlsx"):
    """为名称列表搜索坐标"""
    
    corrector = CoordinateCorrector()
    results = []
    
    print(f"开始为 {len(names_list)} 个名称搜索坐标...")
    
    for i, name in enumerate(names_list, 1):
        print(f"\n[{i}/{len(names_list)}] 搜索: {name}")
        
        try:
            # 获取坐标
            coord_result = corrector.get_corrected_coordinates(name)
            
            if coord_result:
                result = {
                    '名称': name,
                    '经度': coord_result['lng'],
                    '纬度': coord_result['lat'],
                    '数据源': coord_result['source'],
                    '置信度': coord_result['confidence'],
                    '格式化地址': coord_result.get('formatted_address', ''),
                    '搜索查询': coord_result.get('query_used', ''),
                    '状态': '成功'
                }
                print(f"  ✅ 找到坐标: ({coord_result['lat']:.6f}, {coord_result['lng']:.6f})")
                print(f"     地址: {coord_result.get('formatted_address', 'N/A')}")
            else:
                result = {
                    '名称': name,
                    '经度': None,
                    '纬度': None,
                    '数据源': 'N/A',
                    '置信度': 'FAILED',
                    '格式化地址': '',
                    '搜索查询': '',
                    '状态': '未找到'
                }
                print(f"  ❌ 未找到坐标")
            
            results.append(result)
            
            # 每10个请求后稍作延迟，避免API限制
            if i % 10 == 0:
                print(f"  💤 已处理 {i} 个，暂停2秒...")
                time.sleep(2)
                
        except Exception as e:
            print(f"  ❌ 处理失败: {e}")
            result = {
                '名称': name,
                '经度': None,
                '纬度': None,
                '数据源': 'ERROR',
                '置信度': 'ERROR',
                '格式化地址': str(e),
                '搜索查询': '',
                '状态': '错误'
            }
            results.append(result)
    
    # 保存结果
    df_results = pd.DataFrame(results)
    df_results.to_excel(output_file, index=False)
    
    # 统计结果
    success_count = len(df_results[df_results['状态'] == '成功'])
    failed_count = len(df_results[df_results['状态'] == '未找到'])
    error_count = len(df_results[df_results['状态'] == '错误'])
    
    print(f"\n=== 搜索完成 ===")
    print(f"总计: {len(results)} 个名称")
    print(f"成功: {success_count} 个")
    print(f"未找到: {failed_count} 个")
    print(f"错误: {error_count} 个")
    print(f"成功率: {success_count/len(results)*100:.1f}%")
    print(f"结果已保存到: {output_file}")
    
    return df_results

def main():
    """主函数"""
    # 指定要处理的Excel文件
    excel_file = "台湾隧道特定表格.xlsx"  # 请根据实际文件名修改
    
    print("=== 台湾隧道坐标搜索程序 ===")
    print(f"目标文件: {excel_file}")
    
    # 1. 提取第一列的所有名称
    print("\n步骤1: 提取表格第一列名称...")
    names = extract_first_column_names(excel_file)
    
    if not names:
        print("未找到任何有效名称，程序退出")
        return
    
    print(f"共提取到 {len(names)} 个唯一名称")
    
    # 显示前10个名称作为预览
    print("\n前10个名称预览:")
    for i, name in enumerate(names[:10], 1):
        print(f"  {i}. {name}")
    
    if len(names) > 10:
        print(f"  ... 还有 {len(names)-10} 个名称")
    
    # 2. 搜索坐标
    print(f"\n步骤2: 开始搜索坐标...")
    results_df = search_coordinates_for_names(names)
    
    print("\n程序执行完成！")

if __name__ == "__main__":
    main()
