#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索全部縣市加油站位置坐标
使用第二列+第三列+第五列组成地点进行搜索
"""

import pandas as pd
import requests
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import re

class GasStationCoordinateSearcher:
    """加油站坐标搜索器"""
    
    def __init__(self):
        self.google_api_key = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"
        self.session = requests.Session()
        
        # 台湾的地理范围
        self.taiwan_bounds = {
            'min_lat': 21.5, 'max_lat': 25.5,
            'min_lng': 119.5, 'max_lng': 122.5
        }
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful': 0,
            'failed': 0,
            'errors': 0
        }
        self.stats_lock = threading.Lock()
    
    def is_in_taiwan(self, lat, lng):
        """检查坐标是否在台湾范围内"""
        return (self.taiwan_bounds['min_lat'] <= lat <= self.taiwan_bounds['max_lat'] and
                self.taiwan_bounds['min_lng'] <= lng <= self.taiwan_bounds['max_lng'])
    
    def is_valid_result(self, result, search_address):
        """验证搜索结果是否有效"""
        address = result['formatted_address']
        location = result['geometry']['location']
        types = result.get('types', [])
        
        # 检查是否只是台湾的通用坐标
        if address.strip() in ['台灣', 'Taiwan']:
            return False, "只返回台湾通用地址"
        
        # 检查是否是国家级别的结果
        if 'country' in types and 'political' in types:
            return False, "返回国家级别结果"
        
        # 检查坐标是否是台湾中心点
        lat, lng = location['lat'], location['lng']
        if abs(lat - 23.697810) < 0.001 and abs(lng - 120.960515) < 0.001:
            return False, "返回台湾中心坐标"
        
        return True, "有效结果"
    
    def generate_search_queries(self, address_parts):
        """生成搜索查询"""
        col2, col3, col5 = address_parts
        
        # 清理地址部分
        def clean_address(addr):
            if pd.isna(addr):
                return ""
            return str(addr).strip()
        
        col2 = clean_address(col2)
        col3 = clean_address(col3)
        col5 = clean_address(col5)
        
        queries = []
        
        # 基础组合查询
        full_address = f"{col2} {col3} {col5}".strip()
        if full_address:
            queries.extend([
                f"台湾 {full_address}",
                f"{full_address} 台湾",
                f"{full_address} Taiwan",
                full_address
            ])
        
        # 两两组合查询
        if col2 and col3:
            queries.extend([
                f"台湾 {col2} {col3}",
                f"{col2} {col3} 台湾"
            ])
        
        if col2 and col5:
            queries.extend([
                f"台湾 {col2} {col5}",
                f"{col2} {col5} 台湾"
            ])
        
        if col3 and col5:
            queries.extend([
                f"台湾 {col3} {col5}",
                f"{col3} {col5} 台湾"
            ])
        
        # 加油站相关查询
        if full_address:
            queries.extend([
                f"台湾 {full_address} 加油站",
                f"{full_address} 加油站 台湾",
                f"台湾 {full_address} gas station"
            ])
        
        return queries
    
    def search_single_gas_station(self, row_data, thread_id):
        """搜索单个加油站坐标"""

        # 提取地址信息和行索引
        row_index = row_data.get('原始行号', 0)
        station_name = row_data.get('加油站名称', '未知加油站')
        col2 = row_data.get('第二列', '')
        col3 = row_data.get('第三列', '')
        col5 = row_data.get('第五列', '')

        address_parts = (col2, col3, col5)
        search_address = f"{col2} {col3} {col5}".strip()

        print(f"[线程{thread_id:2d}] 🔍 第{row_index+1}行: {station_name} - {search_address}")

        # 生成搜索查询
        queries = self.generate_search_queries(address_parts)

        # 尝试搜索
        for query_idx, query in enumerate(queries, 1):
            if not query.strip():
                continue

            try:
                url = "https://maps.googleapis.com/maps/api/geocode/json"
                params = {
                    'address': query,
                    'key': self.google_api_key,
                    'region': 'tw',
                    'language': 'zh-TW'
                }

                response = self.session.get(url, params=params, timeout=10)

                with self.stats_lock:
                    self.stats['total_requests'] += 1

                if response.status_code == 200:
                    data = response.json()

                    if data['status'] == 'OK' and data['results']:
                        result = data['results'][0]
                        location = result['geometry']['location']
                        lat, lng = location['lat'], location['lng']

                        # 验证是否在台湾范围内
                        if self.is_in_taiwan(lat, lng):
                            # 验证结果有效性
                            is_valid, reason = self.is_valid_result(result, search_address)

                            if is_valid:
                                with self.stats_lock:
                                    self.stats['successful'] += 1

                                print(f"[线程{thread_id:2d}] ✅ 第{row_index+1}行: ({lat:.6f}, {lng:.6f})")

                                return {
                                    '原始行号': row_index,
                                    '经度': lng,
                                    '纬度': lat,
                                    '格式化地址': result['formatted_address'],
                                    '搜索查询': query,
                                    '状态': '成功'
                                }
                            else:
                                print(f"[线程{thread_id:2d}] ⚠️ 第{row_index+1}行 查询{query_idx}无效: {reason}")

                # 短暂延迟
                time.sleep(0.05)

            except Exception as e:
                print(f"[线程{thread_id:2d}] ⚠️ 第{row_index+1}行 查询{query_idx}失败: {e}")
                continue

        # 所有查询都失败
        with self.stats_lock:
            self.stats['failed'] += 1

        print(f"[线程{thread_id:2d}] ❌ 第{row_index+1}行: 未找到有效坐标")

        return {
            '原始行号': row_index,
            '经度': None,
            '纬度': None,
            '格式化地址': '',
            '搜索查询': '',
            '状态': '未找到'
        }

def load_gas_station_data(excel_file):
    """加载加油站数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        print(f"📊 原始数据: {len(df)} 行, {len(df.columns)} 列")
        print(f"📋 列名: {list(df.columns)}")
        
        # 检查列数
        if len(df.columns) < 5:
            print(f"❌ 列数不足，需要至少5列，实际只有{len(df.columns)}列")
            return None
        
        # 保留原始数据，不重命名列
        df_processed = df.copy()

        print(f"📋 原始列名: {list(df.columns)}")
        print(f"📊 数据形状: {df.shape}")

        # 显示第一列的前几个值来确认名称
        if len(df.columns) > 0:
            first_col_name = df.columns[0]
            print(f"📍 第一列名称: '{first_col_name}'")
            print(f"📍 第一列前5个值: {list(df.iloc[:5, 0])}")

        # 重命名列以便后续处理
        df_processed.columns = [f'第{i+1}列' for i in range(len(df.columns))]

        # 添加加油站名称（使用第一列）
        if '第1列' in df_processed.columns:
            df_processed['加油站名称'] = df_processed['第1列'].fillna('未知加油站')
        else:
            df_processed['加油站名称'] = [f'加油站{i+1}' for i in range(len(df_processed))]
        
        print(f"✅ 数据加载成功")
        print(f"📍 将使用: 第2列 + 第3列 + 第5列 组成搜索地址")
        
        # 显示前5行示例
        print(f"\n📋 前5行数据示例:")
        for i in range(min(5, len(df_processed))):
            row = df_processed.iloc[i]
            col2 = row.get('第2列', '')
            col3 = row.get('第3列', '')
            col5 = row.get('第5列', '')
            name = row.get('加油站名称', '')
            search_addr = f"{col2} {col3} {col5}".strip()
            print(f"  {i+1}. {name} - {search_addr}")
        
        return df_processed
        
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None

def concurrent_search_gas_stations(df, max_workers=50):
    """并发搜索加油站坐标"""

    searcher = GasStationCoordinateSearcher()
    search_results = {}  # 使用字典存储结果，键为行号

    print(f"\n🚀 开始50并发搜索 {len(df)} 个加油站坐标")
    print(f"⚡ 并发线程数: {max_workers}")

    start_time = time.time()

    # 准备搜索数据
    search_data = []
    for idx, row in df.iterrows():
        row_data = {
            '原始行号': idx,
            '加油站名称': row.iloc[4] if len(row) >= 5 else f'加油站{idx+1}',  # 第5列：加油站名稱
            '第二列': row.iloc[1] if len(row) >= 2 else '',  # 第2列：縣市
            '第三列': row.iloc[2] if len(row) >= 3 else '',  # 第3列：鄉鎮市區
            '第五列': row.iloc[5] if len(row) >= 6 else ''   # 第6列：地址
        }
        search_data.append(row_data)

    # 并发搜索
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_data = {
            executor.submit(searcher.search_single_gas_station, data, (i % max_workers) + 1): data
            for i, data in enumerate(search_data)
        }

        completed = 0
        for future in as_completed(future_to_data):
            result = future.result()
            row_index = result['原始行号']
            search_results[row_index] = result
            completed += 1

            if completed % 50 == 0 or completed == len(search_data):
                elapsed = time.time() - start_time
                rate = completed / elapsed if elapsed > 0 else 0
                print(f"📊 进度: {completed}/{len(search_data)} ({completed/len(search_data)*100:.1f}%) - {rate:.1f}/秒")

    elapsed = time.time() - start_time
    print(f"⏱️ 搜索完成，总耗时: {elapsed:.1f}秒")

    return search_results, searcher.stats

def main():
    """主函数"""
    excel_file = "全部縣市加油站位置.xlsx"
    output_file = "加油站坐标搜索结果.csv"
    
    print("=== 台湾加油站坐标搜索程序 ===")
    print(f"📁 输入文件: {excel_file}")
    print(f"💾 输出文件: {output_file}")
    print("🔍 搜索策略: 縣市 + 鄉鎮市區 + 地址 组成搜索地址")
    print("📋 名称来源: 第5列 (加油站名稱)")
    
    # 1. 加载数据
    print(f"\n步骤1: 加载加油站数据...")
    df = load_gas_station_data(excel_file)
    
    if df is None:
        print("❌ 数据加载失败，程序退出")
        return
    
    # 2. 并发搜索坐标
    print(f"\n步骤2: 开始并发搜索坐标...")
    search_results, stats = concurrent_search_gas_stations(df, max_workers=50)

    # 3. 创建简化结果表格：只包含名称和经纬度
    print(f"\n步骤3: 创建简化结果表格...")

    # 创建结果列表，保持原始行顺序
    result_data = []
    success_count = 0
    failed_count = 0

    for idx in range(len(df)):
        # 获取加油站名称（使用第5列：加油站名稱）
        if len(df.columns) >= 5:
            station_name = df.iloc[idx, 4]  # 第5列，索引为4
            # 处理可能的NaN值
            if pd.isna(station_name):
                station_name = f'加油站{idx+1}'
            else:
                station_name = str(station_name).strip()
        else:
            station_name = f'加油站{idx+1}'

        # 初始化行数据
        row_data = {
            '名称': station_name,
            '经度': None,
            '纬度': None
        }

        # 如果有搜索结果，填入坐标
        if idx in search_results:
            result = search_results[idx]
            if result['状态'] == '成功':
                row_data['经度'] = result['经度']
                row_data['纬度'] = result['纬度']
                success_count += 1
            else:
                failed_count += 1
        else:
            failed_count += 1

        result_data.append(row_data)

    # 创建最终DataFrame
    df_final = pd.DataFrame(result_data)

    # 4. 保存结果为CSV
    print(f"\n步骤4: 保存搜索结果...")
    df_final.to_csv(output_file, index=False, encoding='utf-8-sig')

    # 5. 统计结果
    total_requests = stats['total_requests']

    print(f"\n" + "="*60)
    print(f"🎉 搜索完成！")
    print(f"📊 总计: {len(df_final)} 个加油站")
    print(f"✅ 成功: {success_count} 个 ({success_count/len(df_final)*100:.1f}%)")
    print(f"❌ 失败: {failed_count} 个")
    print(f"🌐 API请求: {total_requests} 次")
    print(f"💾 结果已保存到: {output_file}")
    print(f"📋 CSV格式：名称 | 经度 | 纬度")
    print(f"📍 未找到坐标的行经纬度列为空")

    # 显示成功率统计
    if success_count > 0:
        print(f"\n📍 成功找到坐标的加油站示例:")
        successful_rows = df_final[df_final['经度'].notna()].head(5)
        for i, (idx, row) in enumerate(successful_rows.iterrows(), 1):
            name = row['名称']
            lat = row['纬度']
            lng = row['经度']
            print(f"  {i}. {name}: ({lat:.6f}, {lng:.6f})")

    # 显示CSV文件格式预览
    print(f"\n📋 CSV文件格式预览:")
    print(f"名称,经度,纬度")
    for i, (idx, row) in enumerate(df_final.head(3).iterrows(), 1):
        name = row['名称']
        lng = row['经度'] if pd.notna(row['经度']) else ''
        lat = row['纬度'] if pd.notna(row['纬度']) else ''
        print(f"{name},{lng},{lat}")

if __name__ == "__main__":
    main()
