#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
翻译加油站坐标距离分析_过滤后.csv中的加油站名称
使用Google翻译API翻译为简体中文和英文
"""

import pandas as pd
import requests
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import re

class GoogleTranslator:
    """Google翻译器"""
    
    def __init__(self):
        self.google_api_key = "AIzaSyBVjr2xmiDmiePxIyqTx16CaK0zPCmQ52I"
        self.session = requests.Session()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful': 0,
            'failed': 0
        }
        self.stats_lock = threading.Lock()
    
    def translate_text(self, text, target_language, source_language='auto'):
        """翻译单个文本"""

        if pd.isna(text) or not str(text).strip():
            return None

        text = str(text).strip()

        try:
            url = f"https://translation.googleapis.com/language/translate/v2?key={self.google_api_key}"

            data = {
                'q': text,
                'target': target_language,
                'source': source_language,
                'format': 'text'
            }

            response = self.session.post(url, data=data, timeout=10)

            with self.stats_lock:
                self.stats['total_requests'] += 1

            if response.status_code == 200:
                result = response.json()

                if 'data' in result and 'translations' in result['data']:
                    translated_text = result['data']['translations'][0]['translatedText']

                    with self.stats_lock:
                        self.stats['successful'] += 1

                    return translated_text
                else:
                    with self.stats_lock:
                        self.stats['failed'] += 1
                    print(f"    API响应格式错误: {result}")
                    return None
            else:
                with self.stats_lock:
                    self.stats['failed'] += 1
                print(f"    API错误 {response.status_code}: {response.text}")
                return None

        except Exception as e:
            with self.stats_lock:
                self.stats['failed'] += 1
            print(f"    翻译异常: {e}")
            return None
    
    def translate_single_name(self, row_data, thread_id):
        """翻译单个加油站名称"""
        
        row_index = row_data['原始行号']
        original_name = row_data['原始名称']
        
        print(f"[线程{thread_id:2d}] 🔍 第{row_index+1}行: {original_name}")
        
        # 翻译为简体中文
        simplified_chinese = self.translate_text(original_name, 'zh-CN')
        time.sleep(0.1)  # 短暂延迟避免API限制
        
        # 翻译为英文
        english = self.translate_text(original_name, 'en')
        time.sleep(0.1)  # 短暂延迟避免API限制
        
        if simplified_chinese and english:
            print(f"[线程{thread_id:2d}] ✅ 第{row_index+1}行: 简中={simplified_chinese}, 英文={english}")
        else:
            print(f"[线程{thread_id:2d}] ❌ 第{row_index+1}行: 翻译失败")
        
        return {
            '原始行号': row_index,
            '原始名称': original_name,
            '简体中文': simplified_chinese,
            '英文': english,
            '状态': '成功' if (simplified_chinese and english) else '失败'
        }

def load_filtered_data(csv_file):
    """加载过滤后的CSV数据"""
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        
        print(f"📊 数据信息:")
        print(f"  行数: {len(df)}")
        print(f"  列数: {len(df.columns)}")
        print(f"  列名: {list(df.columns)}")
        
        # 提取第一列名称
        names = []
        for idx, row in df.iterrows():
            name = row.iloc[0] if len(row) > 0 else f'加油站{idx+1}'
            names.append({
                '原始行号': idx,
                '原始名称': name
            })
        
        print(f"✅ 提取到 {len(names)} 个加油站名称")
        print(f"📍 前5个名称示例:")
        for i in range(min(5, len(names))):
            name = names[i]['原始名称']
            print(f"  {i+1}. {name}")
        
        return df, names
        
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return None, None

def concurrent_translate_names(names, max_workers=10):
    """并发翻译加油站名称"""
    
    translator = GoogleTranslator()
    translation_results = {}
    
    print(f"\n🚀 开始并发翻译 {len(names)} 个加油站名称")
    print(f"⚡ 并发线程数: {max_workers}")
    print("🌐 翻译目标: 简体中文 + 英文")
    
    start_time = time.time()
    
    # 并发翻译
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_data = {
            executor.submit(translator.translate_single_name, name_data, (i % max_workers) + 1): name_data
            for i, name_data in enumerate(names)
        }
        
        completed = 0
        for future in as_completed(future_to_data):
            result = future.result()
            row_index = result['原始行号']
            translation_results[row_index] = result
            completed += 1
            
            if completed % 20 == 0 or completed == len(names):
                elapsed = time.time() - start_time
                rate = completed / elapsed if elapsed > 0 else 0
                print(f"📊 进度: {completed}/{len(names)} ({completed/len(names)*100:.1f}%) - {rate:.1f}/秒")
    
    elapsed = time.time() - start_time
    print(f"⏱️ 翻译完成，总耗时: {elapsed:.1f}秒")
    
    return translation_results, translator.stats

def create_translated_csv(df_original, translation_results, output_file):
    """创建包含翻译的新CSV文件"""
    
    # 复制原始数据
    df_new = df_original.copy()
    
    # 在第一列后插入翻译列
    # 重新排列列顺序：原始名称, 简体中文, 英文, 其他列...
    
    # 添加翻译列
    simplified_chinese_col = []
    english_col = []
    
    for idx in range(len(df_new)):
        if idx in translation_results:
            result = translation_results[idx]
            simplified_chinese_col.append(result['简体中文'])
            english_col.append(result['英文'])
        else:
            simplified_chinese_col.append(None)
            english_col.append(None)
    
    # 插入新列
    df_new.insert(1, '简体中文名称', simplified_chinese_col)
    df_new.insert(2, '英文名称', english_col)
    
    # 保存新文件
    df_new.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    return df_new

def main():
    """主函数"""
    input_file = "加油站坐标距离分析_过滤后.csv"
    output_file = "加油站坐标距离分析_翻译版.csv"
    
    print("=== 加油站名称翻译程序 ===")
    print(f"📁 输入文件: {input_file}")
    print(f"💾 输出文件: {output_file}")
    print("🌐 翻译服务: Google翻译API")
    print("🔤 翻译目标: 简体中文 + 英文")
    
    # 1. 加载数据
    print(f"\n步骤1: 加载过滤后的数据...")
    df_original, names = load_filtered_data(input_file)
    
    if df_original is None or not names:
        print("❌ 数据加载失败")
        return
    
    # 2. 并发翻译
    print(f"\n步骤2: 开始翻译加油站名称...")
    translation_results, stats = concurrent_translate_names(names, max_workers=10)
    
    # 3. 创建翻译版CSV
    print(f"\n步骤3: 创建翻译版CSV文件...")
    df_translated = create_translated_csv(df_original, translation_results, output_file)
    
    # 4. 统计结果
    success_count = sum(1 for r in translation_results.values() if r['状态'] == '成功')
    failed_count = len(translation_results) - success_count
    total_requests = stats['total_requests']
    
    print(f"\n" + "="*60)
    print(f"🎉 翻译完成！")
    print(f"📊 总计: {len(names)} 个名称")
    print(f"✅ 成功: {success_count} 个 ({success_count/len(names)*100:.1f}%)")
    print(f"❌ 失败: {failed_count} 个")
    print(f"🌐 API请求: {total_requests} 次")
    print(f"💾 结果已保存到: {output_file}")
    
    # 显示新文件格式
    print(f"\n📋 新CSV文件格式:")
    print(f"原始名称 | 简体中文名称 | 英文名称 | 经度 | 纬度 | ...")
    
    # 显示翻译示例
    if success_count > 0:
        print(f"\n📍 翻译成功示例:")
        successful_results = [r for r in translation_results.values() if r['状态'] == '成功']
        for i, result in enumerate(successful_results[:5], 1):
            original = result['原始名称']
            simplified = result['简体中文']
            english = result['英文']
            print(f"  {i}. {original}")
            print(f"     简中: {simplified}")
            print(f"     英文: {english}")
    
    print(f"\n📝 说明:")
    print(f"- 使用Google翻译API进行翻译")
    print(f"- 翻译失败的行对应列为空")
    print(f"- 保持原始数据的所有其他列不变")

if __name__ == "__main__":
    main()
