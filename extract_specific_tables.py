#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取台湾隧道页面的特定表格
包括：台铁、台湾高铁、台北捷运、新北捷运、长度在1公里以上的隧道、各县市公路隧道
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import re

def get_page_content(url):
    """获取页面内容"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'
        return response.text
    except requests.RequestException as e:
        print(f"获取页面失败: {e}")
        return None

def clean_text(text):
    """清理文本内容"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    # 移除引用标记 [1], [2] 等
    text = re.sub(r'\[\d+\]', '', text)
    # 移除特殊的不可见字符
    text = re.sub(r'[​‌‍]', '', text)
    
    return text

def extract_table_data(table):
    """提取表格数据"""
    rows = table.find_all('tr')
    if len(rows) < 2:
        return None
    
    # 提取表头
    headers = []
    first_row = rows[0]
    header_cells = first_row.find_all(['th', 'td'])
    
    for cell in header_cells:
        header_text = clean_text(cell.get_text())
        if header_text:
            headers.append(header_text)
        else:
            headers.append(f"列{len(headers)+1}")
    
    if not headers:
        return None
    
    # 提取数据行
    data_rows = []
    for row in rows[1:]:
        cells = row.find_all(['td', 'th'])
        if not cells:
            continue
            
        row_data = []
        for cell in cells:
            cell_text = clean_text(cell.get_text())
            row_data.append(cell_text)
        
        # 调整行数据长度
        while len(row_data) < len(headers):
            row_data.append("")
        row_data = row_data[:len(headers)]
        
        # 只保留有数据的行
        if any(cell.strip() for cell in row_data):
            data_rows.append(row_data)
    
    if not data_rows:
        return None
    
    return pd.DataFrame(data_rows, columns=headers)

def find_tables_by_section(soup):
    """根据章节标题查找对应的表格"""
    target_sections = {
        '台鐵': '台铁隧道',
        '台灣高鐵': '台湾高铁隧道',
        '臺北捷運': '台北捷运隧道',
        '新北捷運': '新北捷运隧道',
        '長度在1公里以上的隧道': '长度1公里以上隧道',
        '臺北市': '台北市隧道',
        '新北市': '新北市隧道',
        '基隆市': '基隆市隧道',
        '桃園市': '桃园市隧道',
        '新竹市': '新竹市隧道',
        '新竹縣': '新竹县隧道',
        '苗栗縣': '苗栗县隧道',
        '台中市': '台中市隧道',
        '彰化縣': '彰化县隧道',
        '南投縣': '南投县隧道',
        '雲林縣': '云林县隧道',
        '嘉義市': '嘉义市隧道',
        '嘉義縣': '嘉义县隧道',
        '台南市': '台南市隧道',
        '高雄市': '高雄市隧道',
        '屏東縣': '屏东县隧道',
        '宜蘭縣': '宜兰县隧道',
        '花蓮縣': '花莲县隧道',
        '台東縣': '台东县隧道'
    }
    
    found_tables = []
    
    # 查找所有标题
    headings = soup.find_all(['h2', 'h3', 'h4'])
    
    for heading in headings:
        heading_text = clean_text(heading.get_text())
        
        # 检查是否是目标章节
        matched_section = None
        for section_key, section_name in target_sections.items():
            if section_key in heading_text:
                matched_section = section_name
                break
        
        if matched_section:
            print(f"找到目标章节: {heading_text} -> {matched_section}")
            
            # 查找该章节下的表格
            current = heading.next_sibling
            tables_in_section = []
            
            # 向下查找直到下一个同级或更高级标题
            while current:
                if current.name in ['h1', 'h2', 'h3', 'h4']:
                    # 遇到同级或更高级标题，停止查找
                    current_level = int(current.name[1])
                    heading_level = int(heading.name[1])
                    if current_level <= heading_level:
                        break
                
                if current.name == 'table' or (hasattr(current, 'find_all') and current.find_all('table')):
                    # 找到表格
                    if current.name == 'table':
                        tables_in_section.append(current)
                    else:
                        tables_in_section.extend(current.find_all('table'))
                
                current = current.next_sibling
            
            # 如果没有在直接兄弟节点中找到表格，尝试在后续内容中查找
            if not tables_in_section:
                next_element = heading
                for _ in range(50):  # 最多查找50个后续元素
                    next_element = next_element.find_next()
                    if not next_element:
                        break
                    
                    if next_element.name in ['h1', 'h2', 'h3', 'h4']:
                        # 遇到其他标题，停止查找
                        next_level = int(next_element.name[1])
                        heading_level = int(heading.name[1])
                        if next_level <= heading_level:
                            break
                    
                    if next_element.name == 'table':
                        tables_in_section.append(next_element)
                        break  # 找到一个表格就够了
            
            # 处理找到的表格
            for i, table in enumerate(tables_in_section):
                df = extract_table_data(table)
                if df is not None:
                    table_name = matched_section
                    if len(tables_in_section) > 1:
                        table_name += f"_{i+1}"
                    
                    found_tables.append({
                        'name': table_name,
                        'data': df,
                        'section': heading_text
                    })
                    print(f"  提取表格: {table_name} ({len(df)} 行)")
    
    return found_tables

def main():
    """主函数"""
    url = "https://zh.wikipedia.org/wiki/%E5%8F%B0%E7%81%A3%E9%9A%A7%E9%81%93"
    
    print("开始提取台湾隧道特定表格数据...")
    print(f"目标URL: {url}")
    
    # 获取页面内容
    html_content = get_page_content(url)
    if not html_content:
        print("无法获取页面内容")
        return
    
    # 解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找目标表格
    tables = find_tables_by_section(soup)
    
    if not tables:
        print("未找到任何目标表格")
        return
    
    print(f"\n共找到 {len(tables)} 个目标表格")
    
    # 保存到Excel文件
    output_file = "台湾隧道特定表格.xlsx"
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        for table_info in tables:
            name = table_info['name']
            df = table_info['data']
            section = table_info['section']
            
            # 清理工作表名称
            sheet_name = re.sub(r'[\\/*?:"<>|]', '_', name)
            sheet_name = sheet_name[:31]  # Excel限制
            
            print(f"保存工作表: {sheet_name}")
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"\n数据已保存到: {output_file}")
    
    # 显示统计信息
    print(f"\n=== 提取结果统计 ===")
    total_rows = 0
    for table_info in tables:
        name = table_info['name']
        df = table_info['data']
        section = table_info['section']
        row_count = len(df)
        col_count = len(df.columns)
        total_rows += row_count
        
        print(f"- {name}: {row_count} 行 × {col_count} 列 (来源: {section})")
    
    print(f"\n总计: {len(tables)} 个表格, {total_rows} 行数据")
    print("提取完成！")

if __name__ == "__main__":
    main()
