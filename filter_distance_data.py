#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
过滤加油站坐标距离分析.csv
删除第六列距离大于100米的行
"""

import pandas as pd

def filter_distance_data(input_file, output_file, max_distance=100):
    """过滤距离数据，保留距离小于等于指定值的行"""
    
    try:
        # 读取CSV文件
        df = pd.read_csv(input_file, encoding='utf-8-sig')
        
        print(f"📊 原始数据信息:")
        print(f"  总行数: {len(df)}")
        print(f"  列数: {len(df.columns)}")
        print(f"  列名: {list(df.columns)}")
        
        # 检查是否有距离列
        distance_col = None
        for col in df.columns:
            if '距离' in col or 'distance' in col.lower():
                distance_col = col
                break
        
        if distance_col is None:
            print("❌ 未找到距离列")
            return False
        
        print(f"📍 距离列: {distance_col}")
        
        # 显示原始数据统计
        valid_distances = df[distance_col].dropna()
        print(f"\n📈 原始距离统计:")
        print(f"  有效距离数据: {len(valid_distances)} 行")
        print(f"  空值数据: {len(df) - len(valid_distances)} 行")
        
        if len(valid_distances) > 0:
            print(f"  最小距离: {valid_distances.min():.2f} 米")
            print(f"  最大距离: {valid_distances.max():.2f} 米")
            print(f"  平均距离: {valid_distances.mean():.2f} 米")
        
        # 统计距离分布
        over_limit = len(valid_distances[valid_distances > max_distance])
        within_limit = len(valid_distances[valid_distances <= max_distance])
        
        print(f"\n🔍 距离分布:")
        print(f"  距离 ≤ {max_distance}米: {within_limit} 行")
        print(f"  距离 > {max_distance}米: {over_limit} 行")
        print(f"  空值: {len(df) - len(valid_distances)} 行")
        
        # 过滤数据：保留距离小于等于100米的行，以及距离为空的行
        df_filtered = df[(df[distance_col] <= max_distance) | (df[distance_col].isna())]
        
        print(f"\n✂️ 过滤结果:")
        print(f"  删除行数: {len(df) - len(df_filtered)}")
        print(f"  保留行数: {len(df_filtered)}")
        print(f"  删除比例: {(len(df) - len(df_filtered))/len(df)*100:.1f}%")
        
        # 保存过滤后的数据
        df_filtered.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"\n💾 过滤后数据已保存到: {output_file}")
        
        # 显示过滤后的统计
        filtered_valid_distances = df_filtered[distance_col].dropna()
        if len(filtered_valid_distances) > 0:
            print(f"\n📊 过滤后距离统计:")
            print(f"  有效距离数据: {len(filtered_valid_distances)} 行")
            print(f"  最小距离: {filtered_valid_distances.min():.2f} 米")
            print(f"  最大距离: {filtered_valid_distances.max():.2f} 米")
            print(f"  平均距离: {filtered_valid_distances.mean():.2f} 米")
        
        # 显示被删除的行示例
        deleted_rows = df[df[distance_col] > max_distance]
        if len(deleted_rows) > 0:
            print(f"\n🗑️ 被删除的行示例 (距离 > {max_distance}米):")
            for i, (idx, row) in enumerate(deleted_rows.head(5).iterrows(), 1):
                name = row.iloc[0] if len(row) > 0 else '未知'
                distance = row[distance_col]
                print(f"  {i}. {name}: {distance:.2f} 米")
            
            if len(deleted_rows) > 5:
                print(f"     ... 还有 {len(deleted_rows) - 5} 行被删除")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        return False

def main():
    """主函数"""
    input_file = "加油站坐标距离分析.csv"
    output_file = "加油站坐标距离分析_过滤后.csv"
    max_distance = 100  # 最大允许距离（米）
    
    print("=== 加油站坐标距离数据过滤程序 ===")
    print(f"📁 输入文件: {input_file}")
    print(f"💾 输出文件: {output_file}")
    print(f"🔍 过滤条件: 删除距离 > {max_distance} 米的行")
    print(f"📝 说明: 保留距离 ≤ {max_distance} 米的行和距离为空的行")
    
    # 执行过滤
    success = filter_distance_data(input_file, output_file, max_distance)
    
    if success:
        print(f"\n" + "="*60)
        print(f"🎉 数据过滤完成！")
        print(f"✅ 已删除距离大于 {max_distance} 米的行")
        print(f"💾 过滤后数据保存在: {output_file}")
        print(f"\n📋 过滤逻辑:")
        print(f"  - 保留: 距离 ≤ {max_distance} 米的行")
        print(f"  - 保留: 距离为空值的行")
        print(f"  - 删除: 距离 > {max_distance} 米的行")
    else:
        print(f"\n❌ 数据过滤失败")

if __name__ == "__main__":
    main()
