#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试坐标搜索问题
检查为什么不同隧道返回相同坐标
"""

import requests
import json

def debug_single_search(name, api_key="AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"):
    """调试单个名称的搜索过程"""
    
    print(f"\n{'='*60}")
    print(f"🔍 调试搜索: {name}")
    print(f"{'='*60}")
    
    # 构建不同的搜索查询
    queries = [
        f"台湾 {name}",
        f"{name} 台湾", 
        f"{name} Taiwan",
        f"台湾省 {name}",
        f"{name}",  # 不加前缀
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n--- 查询 {i}: '{query}' ---")
        
        try:
            url = "https://maps.googleapis.com/maps/api/geocode/json"
            params = {
                'address': query,
                'key': api_key,
                'region': 'tw',
                'language': 'zh-TW'
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                print(f"状态: {data['status']}")
                
                if data['status'] == 'OK' and data['results']:
                    print(f"结果数量: {len(data['results'])}")
                    
                    # 显示前3个结果
                    for j, result in enumerate(data['results'][:3]):
                        location = result['geometry']['location']
                        lat, lng = location['lat'], location['lng']
                        address = result['formatted_address']
                        
                        print(f"  结果 {j+1}:")
                        print(f"    坐标: ({lat:.6f}, {lng:.6f})")
                        print(f"    地址: {address}")
                        print(f"    类型: {result.get('types', [])}")
                        
                        # 检查是否在台湾范围内
                        in_taiwan = (21.5 <= lat <= 25.5 and 119.5 <= lng <= 122.5)
                        print(f"    在台湾范围: {in_taiwan}")
                        
                        if j == 0:  # 第一个结果是API会返回的
                            print(f"    ⭐ 这是API返回的第一个结果")
                else:
                    print(f"❌ 无结果: {data.get('error_message', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

def main():
    """主函数 - 调试问题隧道"""
    
    print("🔧 台湾隧道坐标搜索问题调试")
    
    # 测试有问题的三个隧道
    problem_tunnels = [
        "新五堵",
        "苗南隧道", 
        "伍份隧道"
    ]
    
    print(f"📋 将调试以下 {len(problem_tunnels)} 个隧道:")
    for i, name in enumerate(problem_tunnels, 1):
        print(f"  {i}. {name}")
    
    # 逐个调试
    for tunnel in problem_tunnels:
        debug_single_search(tunnel)
    
    print(f"\n{'='*60}")
    print("🔍 调试完成")
    print("📝 请检查:")
    print("1. 不同查询是否返回相同坐标")
    print("2. 是否所有查询都返回'台湾'这个通用地址")
    print("3. 具体的隧道名称是否被正确识别")
    print("4. 是否需要更精确的搜索词")

if __name__ == "__main__":
    main()
