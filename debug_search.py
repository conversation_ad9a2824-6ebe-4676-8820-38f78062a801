#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试坐标搜索问题
检查为什么不同隧道返回相同坐标
"""

import requests
import json

def is_valid_result(result, search_name):
    """验证搜索结果是否有效"""
    address = result['formatted_address']
    location = result['geometry']['location']
    types = result.get('types', [])

    # 检查是否只是台湾的通用坐标
    if address.strip() == '台灣' or address.strip() == 'Taiwan':
        return False, "只返回台湾通用地址"

    # 检查是否是国家级别的结果
    if 'country' in types and 'political' in types:
        return False, "返回国家级别结果"

    # 检查坐标是否是台湾中心点 (大约23.7, 120.96)
    lat, lng = location['lat'], location['lng']
    if abs(lat - 23.697810) < 0.001 and abs(lng - 120.960515) < 0.001:
        return False, "返回台湾中心坐标"

    # 检查地址中是否包含搜索名称的关键词
    name_keywords = search_name.replace('隧道', '').replace('tunnel', '').strip()
    if name_keywords and name_keywords not in address:
        return False, f"地址中不包含关键词'{name_keywords}'"

    return True, "有效结果"

def generate_search_queries(name):
    """生成更多样化的搜索查询"""
    queries = []

    # 基础查询
    queries.extend([
        f"台湾 {name}",
        f"{name} 台湾",
        f"{name} Taiwan",
        f"台湾省 {name}",
    ])

    # 如果包含"隧道"，尝试不同的表达方式
    if "隧道" in name:
        base_name = name.replace("隧道", "")
        queries.extend([
            f"台湾 {base_name} 隧道",
            f"{base_name} tunnel Taiwan",
            f"台湾 {base_name} tunnel",
            f"{base_name} 隧道 台湾",
        ])

    # 添加地理位置相关词汇
    queries.extend([
        f"台湾 {name} 位置",
        f"台湾 {name} 地点",
        f"{name} location Taiwan",
        f"{name} 坐标 台湾",
    ])

    # 添加交通相关词汇
    queries.extend([
        f"台湾 {name} 交通",
        f"台湾铁路 {name}",
        f"台湾公路 {name}",
        f"{name} railway Taiwan",
        f"{name} highway Taiwan",
    ])

    # 尝试繁体字变体
    simplified_to_traditional = {
        '隧道': '隧道',
        '铁路': '鐵路',
        '公路': '公路',
        '台湾': '台灣',
    }

    traditional_name = name
    for simp, trad in simplified_to_traditional.items():
        traditional_name = traditional_name.replace(simp, trad)

    if traditional_name != name:
        queries.extend([
            f"台灣 {traditional_name}",
            f"{traditional_name} 台灣",
        ])

    return queries

def debug_single_search(name, api_key="AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"):
    """调试单个名称的搜索过程 - 改进版"""

    print(f"\n{'='*60}")
    print(f"🔍 调试搜索: {name}")
    print(f"{'='*60}")

    # 生成多样化的搜索查询
    queries = generate_search_queries(name)
    print(f"📝 生成了 {len(queries)} 个搜索查询")

    valid_results = []
    invalid_results = []

    for i, query in enumerate(queries, 1):
        print(f"\n--- 查询 {i}: '{query}' ---")

        try:
            url = "https://maps.googleapis.com/maps/api/geocode/json"
            params = {
                'address': query,
                'key': api_key,
                'region': 'tw',
                'language': 'zh-TW'
            }

            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()

                print(f"状态: {data['status']}")

                if data['status'] == 'OK' and data['results']:
                    print(f"结果数量: {len(data['results'])}")

                    # 验证并显示结果
                    for j, result in enumerate(data['results'][:3]):
                        location = result['geometry']['location']
                        lat, lng = location['lat'], location['lng']
                        address = result['formatted_address']
                        types = result.get('types', [])

                        print(f"  结果 {j+1}:")
                        print(f"    坐标: ({lat:.6f}, {lng:.6f})")
                        print(f"    地址: {address}")
                        print(f"    类型: {types}")

                        # 检查是否在台湾范围内
                        in_taiwan = (21.5 <= lat <= 25.5 and 119.5 <= lng <= 122.5)
                        print(f"    在台湾范围: {in_taiwan}")

                        # 验证结果有效性
                        is_valid, reason = is_valid_result(result, name)
                        print(f"    结果有效性: {'✅ 有效' if is_valid else '❌ 无效'} - {reason}")

                        if j == 0:  # 第一个结果
                            print(f"    ⭐ 这是API返回的第一个结果")
                            if is_valid:
                                valid_results.append({
                                    'query': query,
                                    'result': result,
                                    'coordinates': (lat, lng),
                                    'address': address
                                })
                            else:
                                invalid_results.append({
                                    'query': query,
                                    'result': result,
                                    'reason': reason
                                })

                        # 如果找到有效结果，可以提前结束
                        if is_valid and j == 0:
                            print(f"    🎯 找到有效结果，可以使用此坐标")
                            break
                else:
                    print(f"❌ 无结果: {data.get('error_message', '未知错误')}")
            else:
                print(f"❌ HTTP错误: {response.status_code}")

        except Exception as e:
            print(f"❌ 请求异常: {e}")

        # 如果已经找到有效结果，可以选择是否继续搜索
        if valid_results:
            print(f"✅ 已找到 {len(valid_results)} 个有效结果")
            # 可以选择继续搜索更多结果或提前结束
            # break  # 取消注释此行可在找到第一个有效结果后停止

    # 总结搜索结果
    print(f"\n{'='*40}")
    print(f"📊 搜索总结: {name}")
    print(f"{'='*40}")
    print(f"✅ 有效结果: {len(valid_results)} 个")
    print(f"❌ 无效结果: {len(invalid_results)} 个")

    if valid_results:
        print(f"\n🎯 推荐使用的坐标:")
        best_result = valid_results[0]
        print(f"   查询: {best_result['query']}")
        print(f"   坐标: {best_result['coordinates']}")
        print(f"   地址: {best_result['address']}")
    else:
        print(f"\n❌ 未找到任何有效坐标")
        if invalid_results:
            print(f"   主要问题: {invalid_results[0]['reason']}")

    return valid_results, invalid_results

def main():
    """主函数 - 调试问题隧道"""

    print("🔧 台湾隧道坐标搜索问题调试 - 改进版")
    print("✨ 新功能:")
    print("  - 多样化搜索策略")
    print("  - 结果有效性验证")
    print("  - 过滤台湾通用坐标")

    # 测试有问题的三个隧道
    problem_tunnels = [
        "新五堵",
        "苗南隧道",
        "伍份隧道"
    ]

    print(f"\n📋 将调试以下 {len(problem_tunnels)} 个隧道:")
    for i, name in enumerate(problem_tunnels, 1):
        print(f"  {i}. {name}")

    # 逐个调试并收集结果
    all_valid_results = {}
    all_invalid_results = {}

    for tunnel in problem_tunnels:
        valid_results, invalid_results = debug_single_search(tunnel)
        all_valid_results[tunnel] = valid_results
        all_invalid_results[tunnel] = invalid_results

    # 最终总结
    print(f"\n{'='*60}")
    print("🎯 最终调试总结")
    print(f"{'='*60}")

    for tunnel in problem_tunnels:
        valid_count = len(all_valid_results[tunnel])
        invalid_count = len(all_invalid_results[tunnel])

        print(f"\n� {tunnel}:")
        if valid_count > 0:
            best_result = all_valid_results[tunnel][0]
            print(f"  ✅ 状态: 找到有效坐标")
            print(f"  📍 推荐坐标: {best_result['coordinates']}")
            print(f"  🏠 地址: {best_result['address']}")
            print(f"  🔍 最佳查询: {best_result['query']}")
        else:
            print(f"  ❌ 状态: 未找到有效坐标")
            if invalid_count > 0:
                main_issue = all_invalid_results[tunnel][0]['reason']
                print(f"  ⚠️  主要问题: {main_issue}")

    print(f"\n📝 改进建议:")
    print("1. 对于找到有效坐标的隧道，可以直接使用")
    print("2. 对于未找到的隧道，可能需要:")
    print("   - 手动查找更准确的名称")
    print("   - 使用台湾本地地理数据库")
    print("   - 添加更多上下文信息（如所在县市）")
    print("3. 在批量搜索中应该过滤掉'台湾通用坐标'")

    return all_valid_results, all_invalid_results

if __name__ == "__main__":
    main()
