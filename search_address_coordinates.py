#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索全部縣市加油站位置.xlsx第六列地址的坐标
添加到加油站坐标搜索结果.csv的第5、6列
"""

import pandas as pd
import requests
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class AddressCoordinateSearcher:
    """地址坐标搜索器"""
    
    def __init__(self):
        self.google_api_key = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"
        self.session = requests.Session()
        
        # 台湾的地理范围
        self.taiwan_bounds = {
            'min_lat': 21.5, 'max_lat': 25.5,
            'min_lng': 119.5, 'max_lng': 122.5
        }
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful': 0,
            'failed': 0
        }
        self.stats_lock = threading.Lock()
    
    def is_in_taiwan(self, lat, lng):
        """检查坐标是否在台湾范围内"""
        return (self.taiwan_bounds['min_lat'] <= lat <= self.taiwan_bounds['max_lat'] and
                self.taiwan_bounds['min_lng'] <= lng <= self.taiwan_bounds['max_lng'])
    
    def is_valid_result(self, result):
        """验证搜索结果是否有效"""
        address = result['formatted_address']
        location = result['geometry']['location']
        types = result.get('types', [])
        
        # 检查是否只是台湾的通用坐标
        if address.strip() in ['台灣', 'Taiwan']:
            return False, "只返回台湾通用地址"
        
        # 检查是否是国家级别的结果
        if 'country' in types and 'political' in types:
            return False, "返回国家级别结果"
        
        # 检查坐标是否是台湾中心点
        lat, lng = location['lat'], location['lng']
        if abs(lat - 23.697810) < 0.001 and abs(lng - 120.960515) < 0.001:
            return False, "返回台湾中心坐标"
        
        return True, "有效结果"
    
    def generate_address_queries(self, address):
        """生成地址搜索查询"""
        if pd.isna(address) or not str(address).strip():
            return []
        
        address = str(address).strip()
        queries = []
        
        # 基础查询
        queries.extend([
            f"台湾 {address}",
            f"{address} 台湾",
            f"{address} Taiwan",
            address
        ])
        
        # 如果地址中没有台湾相关词汇，添加更多变体
        if '台湾' not in address and '台灣' not in address:
            queries.extend([
                f"台灣 {address}",
                f"{address} 台灣"
            ])
        
        return queries
    
    def search_single_address(self, row_data, thread_id):
        """搜索单个地址的坐标"""
        
        row_index = row_data['原始行号']
        address = row_data['地址']
        
        print(f"[线程{thread_id:2d}] 🔍 第{row_index+1}行地址: {address}")
        
        # 生成搜索查询
        queries = self.generate_address_queries(address)
        
        if not queries:
            print(f"[线程{thread_id:2d}] ❌ 第{row_index+1}行: 地址为空")
            return {
                '原始行号': row_index,
                '地址经度': None,
                '地址纬度': None,
                '状态': '地址为空'
            }
        
        # 尝试搜索
        for query_idx, query in enumerate(queries, 1):
            try:
                url = "https://maps.googleapis.com/maps/api/geocode/json"
                params = {
                    'address': query,
                    'key': self.google_api_key,
                    'region': 'tw',
                    'language': 'zh-TW'
                }
                
                response = self.session.get(url, params=params, timeout=10)
                
                with self.stats_lock:
                    self.stats['total_requests'] += 1
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data['status'] == 'OK' and data['results']:
                        result = data['results'][0]
                        location = result['geometry']['location']
                        lat, lng = location['lat'], location['lng']
                        
                        # 验证是否在台湾范围内
                        if self.is_in_taiwan(lat, lng):
                            # 验证结果有效性
                            is_valid, reason = self.is_valid_result(result)
                            
                            if is_valid:
                                with self.stats_lock:
                                    self.stats['successful'] += 1
                                
                                print(f"[线程{thread_id:2d}] ✅ 第{row_index+1}行: ({lat:.6f}, {lng:.6f})")
                                
                                return {
                                    '原始行号': row_index,
                                    '地址经度': lng,
                                    '地址纬度': lat,
                                    '状态': '成功'
                                }
                            else:
                                print(f"[线程{thread_id:2d}] ⚠️ 第{row_index+1}行 查询{query_idx}无效: {reason}")
                
                # 短暂延迟
                time.sleep(0.05)
                
            except Exception as e:
                print(f"[线程{thread_id:2d}] ⚠️ 第{row_index+1}行 查询{query_idx}失败: {e}")
                continue
        
        # 所有查询都失败
        with self.stats_lock:
            self.stats['failed'] += 1
        
        print(f"[线程{thread_id:2d}] ❌ 第{row_index+1}行: 未找到有效坐标")
        
        return {
            '原始行号': row_index,
            '地址经度': None,
            '地址纬度': None,
            '状态': '未找到'
        }

def load_excel_addresses(excel_file):
    """从Excel文件加载地址数据"""
    try:
        df = pd.read_excel(excel_file)
        
        print(f"📊 Excel数据: {len(df)} 行, {len(df.columns)} 列")
        print(f"📋 列名: {list(df.columns)}")
        
        if len(df.columns) < 6:
            print(f"❌ 列数不足，需要至少6列，实际只有{len(df.columns)}列")
            return None
        
        # 提取第6列地址
        addresses = []
        for idx, row in df.iterrows():
            address = row.iloc[5] if len(row) >= 6 else ''  # 第6列，索引为5
            addresses.append({
                '原始行号': idx,
                '地址': address
            })
        
        print(f"✅ 提取到 {len(addresses)} 个地址")
        print(f"📍 前5个地址示例:")
        for i in range(min(5, len(addresses))):
            addr = addresses[i]['地址']
            print(f"  {i+1}. {addr}")
        
        return addresses
        
    except Exception as e:
        print(f"❌ 加载Excel失败: {e}")
        return None

def load_existing_csv(csv_file):
    """加载现有的CSV文件"""
    try:
        df = pd.read_csv(csv_file, encoding='utf-8-sig')
        print(f"📊 现有CSV: {len(df)} 行, {len(df.columns)} 列")
        print(f"📋 列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"❌ 加载CSV失败: {e}")
        return None

def concurrent_search_addresses(addresses, max_workers=50):
    """并发搜索地址坐标"""
    
    searcher = AddressCoordinateSearcher()
    search_results = {}
    
    print(f"\n🚀 开始50并发搜索 {len(addresses)} 个地址坐标")
    print(f"⚡ 并发线程数: {max_workers}")
    
    start_time = time.time()
    
    # 并发搜索
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_data = {
            executor.submit(searcher.search_single_address, addr_data, (i % max_workers) + 1): addr_data
            for i, addr_data in enumerate(addresses)
        }
        
        completed = 0
        for future in as_completed(future_to_data):
            result = future.result()
            row_index = result['原始行号']
            search_results[row_index] = result
            completed += 1
            
            if completed % 100 == 0 or completed == len(addresses):
                elapsed = time.time() - start_time
                rate = completed / elapsed if elapsed > 0 else 0
                print(f"📊 进度: {completed}/{len(addresses)} ({completed/len(addresses)*100:.1f}%) - {rate:.1f}/秒")
    
    elapsed = time.time() - start_time
    print(f"⏱️ 搜索完成，总耗时: {elapsed:.1f}秒")
    
    return search_results, searcher.stats

def main():
    """主函数"""
    excel_file = "全部縣市加油站位置.xlsx"
    csv_file = "加油站坐标搜索结果.csv"
    output_file = "加油站坐标搜索结果_完整版.csv"
    
    print("=== 地址坐标搜索程序 ===")
    print(f"📁 Excel输入: {excel_file}")
    print(f"📁 CSV输入: {csv_file}")
    print(f"💾 输出文件: {output_file}")
    print("🔍 搜索目标: 第6列地址坐标")
    print("📋 添加到CSV的第5、6列")
    
    # 1. 加载Excel地址数据
    print(f"\n步骤1: 从Excel加载地址数据...")
    addresses = load_excel_addresses(excel_file)
    
    if not addresses:
        print("❌ 地址数据加载失败")
        return
    
    # 2. 加载现有CSV
    print(f"\n步骤2: 加载现有CSV文件...")
    df_csv = load_existing_csv(csv_file)
    
    if df_csv is None:
        print("❌ CSV文件加载失败")
        return
    
    # 3. 并发搜索地址坐标
    print(f"\n步骤3: 开始搜索地址坐标...")
    search_results, stats = concurrent_search_addresses(addresses, max_workers=50)
    
    # 4. 合并结果
    print(f"\n步骤4: 合并搜索结果...")
    
    # 复制现有CSV数据
    df_final = df_csv.copy()
    
    # 添加地址坐标列
    df_final['地址经度'] = None
    df_final['地址纬度'] = None
    
    # 填入搜索结果
    success_count = 0
    failed_count = 0
    
    for idx in range(len(df_final)):
        if idx in search_results:
            result = search_results[idx]
            if result['状态'] == '成功':
                df_final.loc[idx, '地址经度'] = result['地址经度']
                df_final.loc[idx, '地址纬度'] = result['地址纬度']
                success_count += 1
            else:
                failed_count += 1
        else:
            failed_count += 1
    
    # 5. 保存结果
    print(f"\n步骤5: 保存完整结果...")
    df_final.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    # 6. 统计结果
    total_requests = stats['total_requests']
    
    print(f"\n" + "="*60)
    print(f"🎉 地址坐标搜索完成！")
    print(f"📊 总计: {len(df_final)} 个地址")
    print(f"✅ 成功: {success_count} 个 ({success_count/len(df_final)*100:.1f}%)")
    print(f"❌ 失败: {failed_count} 个")
    print(f"🌐 API请求: {total_requests} 次")
    print(f"💾 结果已保存到: {output_file}")
    print(f"📋 CSV格式: 名称 | 经度 | 纬度 | 地址经度 | 地址纬度")
    
    # 显示成功示例
    if success_count > 0:
        print(f"\n📍 成功找到地址坐标的示例:")
        successful_rows = df_final[df_final['地址经度'].notna()].head(5)
        for i, (idx, row) in enumerate(successful_rows.iterrows(), 1):
            name = row['名称']
            addr_lng = row['地址经度']
            addr_lat = row['地址纬度']
            print(f"  {i}. {name}: 地址坐标({addr_lat:.6f}, {addr_lng:.6f})")

if __name__ == "__main__":
    main()
