#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的台湾隧道表格提取脚本
直接提取页面中的所有表格，一个表格对应一个Excel工作表
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import re

def get_page_content(url):
    """获取页面内容"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        response.encoding = 'utf-8'
        return response.text
    except requests.RequestException as e:
        print(f"获取页面失败: {e}")
        return None

def clean_text(text):
    """清理文本内容"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    # 移除引用标记 [1], [2] 等
    text = re.sub(r'\[\d+\]', '', text)
    # 移除特殊的不可见字符
    text = re.sub(r'[​‌‍]', '', text)
    
    return text

def extract_table_to_dataframe(table, table_index):
    """将HTML表格转换为DataFrame"""
    
    # 获取所有行
    rows = table.find_all('tr')
    if len(rows) < 2:  # 至少需要表头和一行数据
        return None
    
    print(f"表格 {table_index}: 找到 {len(rows)} 行")
    
    # 提取表头
    headers = []
    first_row = rows[0]
    header_cells = first_row.find_all(['th', 'td'])
    
    for cell in header_cells:
        header_text = clean_text(cell.get_text())
        if header_text:
            headers.append(header_text)
        else:
            headers.append(f"列{len(headers)+1}")
    
    if not headers:
        print(f"表格 {table_index}: 未找到有效表头")
        return None
    
    print(f"表格 {table_index}: 表头 = {headers}")
    
    # 提取数据行
    data_rows = []
    for row_idx, row in enumerate(rows[1:], 1):  # 跳过表头行
        cells = row.find_all(['td', 'th'])
        if not cells:
            continue
            
        row_data = []
        for cell in cells:
            cell_text = clean_text(cell.get_text())
            row_data.append(cell_text)
        
        # 确保行数据长度与表头一致
        while len(row_data) < len(headers):
            row_data.append("")
        
        # 截断多余的列
        row_data = row_data[:len(headers)]
        
        # 只保留至少有一个非空单元格的行
        if any(cell.strip() for cell in row_data):
            data_rows.append(row_data)
    
    if not data_rows:
        print(f"表格 {table_index}: 未找到有效数据行")
        return None
    
    # 创建DataFrame
    df = pd.DataFrame(data_rows, columns=headers)
    print(f"表格 {table_index}: 提取到 {len(df)} 行 × {len(df.columns)} 列数据")
    
    return df

def find_table_context(table):
    """查找表格的上下文信息（所属章节）"""
    context = "未知章节"
    
    # 向上查找最近的标题
    current = table
    for _ in range(20):  # 最多向上查找20个元素
        prev_element = current.find_previous(['h1', 'h2', 'h3', 'h4', 'h5'])
        if prev_element:
            heading_text = clean_text(prev_element.get_text())
            if heading_text and len(heading_text) < 100:  # 避免过长的标题
                context = heading_text
                break
        current = current.parent
        if not current:
            break
    
    return context

def main():
    """主函数"""
    url = "https://zh.wikipedia.org/wiki/%E5%8F%B0%E7%81%A3%E9%9A%A7%E9%81%93"
    
    print("开始提取台湾隧道页面的表格数据...")
    print(f"目标URL: {url}")
    
    # 获取页面内容
    html_content = get_page_content(url)
    if not html_content:
        print("无法获取页面内容，程序退出")
        return
    
    # 解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找所有表格
    all_tables = soup.find_all('table')
    print(f"\n页面中共找到 {len(all_tables)} 个表格元素")
    
    # 提取每个表格的数据
    valid_tables = []
    
    for i, table in enumerate(all_tables, 1):
        print(f"\n--- 处理表格 {i} ---")
        
        # 查找表格所属章节
        context = find_table_context(table)
        print(f"表格 {i}: 所属章节 = {context}")
        
        # 提取表格数据
        df = extract_table_to_dataframe(table, i)
        
        if df is not None:
            valid_tables.append({
                'index': i,
                'context': context,
                'dataframe': df
            })
            print(f"表格 {i}: ✓ 提取成功")
        else:
            print(f"表格 {i}: ✗ 提取失败或无有效数据")
    
    print(f"\n=== 提取结果 ===")
    print(f"总表格数: {len(all_tables)}")
    print(f"有效表格数: {len(valid_tables)}")
    
    if not valid_tables:
        print("没有提取到有效的表格数据")
        return
    
    # 保存到Excel文件
    output_file = "台湾隧道表格数据.xlsx"
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        for table_info in valid_tables:
            table_index = table_info['index']
            context = table_info['context']
            df = table_info['dataframe']
            
            # 生成工作表名称
            sheet_name = f"表{table_index}_{context}"
            # 清理工作表名称（Excel限制）
            sheet_name = re.sub(r'[\\/*?:"<>|]', '_', sheet_name)
            sheet_name = sheet_name[:31]  # Excel工作表名称最长31字符
            
            print(f"保存工作表: {sheet_name}")
            df.to_excel(writer, sheet_name=sheet_name, index=False)
    
    print(f"\n数据已保存到: {output_file}")
    
    # 显示详细统计
    print(f"\n=== 详细统计 ===")
    total_rows = 0
    for table_info in valid_tables:
        table_index = table_info['index']
        context = table_info['context']
        df = table_info['dataframe']
        row_count = len(df)
        col_count = len(df.columns)
        total_rows += row_count
        
        print(f"表格 {table_index} ({context}): {row_count} 行 × {col_count} 列")
    
    print(f"\n总计: {len(valid_tables)} 个表格, {total_rows} 行数据")
    print("提取完成！")

if __name__ == "__main__":
    main()
