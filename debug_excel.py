#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Excel文件内容
"""

import pandas as pd

def debug_excel_file(excel_file):
    """调试Excel文件内容"""
    
    print(f"🔍 调试Excel文件: {excel_file}")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        print(f"\n📊 基本信息:")
        print(f"  行数: {len(df)}")
        print(f"  列数: {len(df.columns)}")
        print(f"  列名: {list(df.columns)}")
        
        print(f"\n📋 前10行数据:")
        for i in range(min(10, len(df))):
            print(f"  第{i+1}行:")
            for j, col in enumerate(df.columns):
                value = df.iloc[i, j]
                print(f"    第{j+1}列 ({col}): {repr(value)}")
            print()
        
        print(f"\n📍 第一列详细信息:")
        if len(df.columns) > 0:
            first_col = df.iloc[:, 0]
            print(f"  列名: {df.columns[0]}")
            print(f"  数据类型: {first_col.dtype}")
            print(f"  非空值数量: {first_col.count()}")
            print(f"  空值数量: {first_col.isna().sum()}")
            print(f"  前10个值:")
            for i in range(min(10, len(first_col))):
                value = first_col.iloc[i]
                print(f"    [{i}] {repr(value)} (类型: {type(value)})")
        
        print(f"\n📍 第2、3、5列信息 (用于地址搜索):")
        for col_idx in [1, 2, 4]:  # 第2、3、5列 (索引1、2、4)
            if col_idx < len(df.columns):
                col = df.iloc[:, col_idx]
                col_name = df.columns[col_idx]
                print(f"  第{col_idx+1}列 ({col_name}):")
                print(f"    前5个值: {list(col.head())}")
            else:
                print(f"  第{col_idx+1}列: 不存在")
        
    except Exception as e:
        print(f"❌ 读取失败: {e}")

if __name__ == "__main__":
    debug_excel_file("全部縣市加油站位置.xlsx")
