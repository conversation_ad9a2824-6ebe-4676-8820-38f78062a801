#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的30并发搜索台湾隧道坐标
同时搜索30个点位，输出分开的副表
"""

import pandas as pd
import requests
import time
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from collections import defaultdict

class CoordinateSearcher:
    """坐标搜索器 - 真正并发版本"""
    
    def __init__(self):
        self.google_api_key = "AIzaSyCATIg0eLhlY3j2NnwEsWP2cqhgCULUrNY"
        self.session = requests.Session()  # 复用连接
        
        # 台湾的地理范围
        self.taiwan_bounds = {
            'min_lat': 21.5, 'max_lat': 25.5,
            'min_lng': 119.5, 'max_lng': 122.5
        }
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful': 0,
            'failed': 0,
            'errors': 0
        }
        self.stats_lock = threading.Lock()
    
    def is_in_taiwan(self, lat, lng):
        """检查坐标是否在台湾范围内"""
        return (self.taiwan_bounds['min_lat'] <= lat <= self.taiwan_bounds['max_lat'] and
                self.taiwan_bounds['min_lng'] <= lng <= self.taiwan_bounds['max_lng'])
    
    def search_single_coordinate(self, name, thread_id):
        """搜索单个坐标 - 真正的并发执行"""
        
        # 构建搜索查询
        queries = [
            f"台湾 {name}",
            f"{name} 台湾",
            f"{name} Taiwan",
            f"台湾省 {name}"
        ]
        
        print(f"[线程{thread_id:2d}] 🔍 开始搜索: {name}")
        
        for query_idx, query in enumerate(queries, 1):
            try:
                url = "https://maps.googleapis.com/maps/api/geocode/json"
                params = {
                    'address': query,
                    'key': self.google_api_key,
                    'region': 'tw',
                    'language': 'zh-TW'
                }
                
                # 发送请求（真正并发，无锁）
                response = self.session.get(url, params=params, timeout=10)
                
                with self.stats_lock:
                    self.stats['total_requests'] += 1
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if data['status'] == 'OK' and data['results']:
                        result = data['results'][0]
                        location = result['geometry']['location']
                        lat, lng = location['lat'], location['lng']
                        
                        # 验证是否在台湾范围内
                        if self.is_in_taiwan(lat, lng):
                            confidence = 'HIGH' if any(keyword in result['formatted_address'] 
                                                     for keyword in ['台湾', 'Taiwan', '台灣']) else 'MEDIUM'
                            
                            with self.stats_lock:
                                self.stats['successful'] += 1
                            
                            print(f"[线程{thread_id:2d}] ✅ {name}: ({lat:.6f}, {lng:.6f}) - 查询{query_idx}")
                            
                            return {
                                '名称': name,
                                '经度': lng,
                                '纬度': lat,
                                '数据源': 'Google Geocoding API',
                                '置信度': confidence,
                                '格式化地址': result['formatted_address'],
                                '搜索查询': query,
                                '状态': '成功',
                                '线程ID': thread_id
                            }
                
                # 短暂延迟后尝试下一个查询
                time.sleep(0.1)
                
            except Exception as e:
                print(f"[线程{thread_id:2d}] ⚠️ {name} 查询{query_idx}失败: {e}")
                continue
        
        # 所有查询都失败
        with self.stats_lock:
            self.stats['failed'] += 1
        
        print(f"[线程{thread_id:2d}] ❌ {name}: 未找到坐标")
        
        return {
            '名称': name,
            '经度': None,
            '纬度': None,
            '数据源': 'N/A',
            '置信度': 'FAILED',
            '格式化地址': '',
            '搜索查询': '',
            '状态': '未找到',
            '线程ID': thread_id
        }

def extract_names_by_category(excel_file):
    """按类别提取名称，返回分类结果"""
    categories = {}
    
    try:
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        for sheet_name, df in excel_data.items():
            if df.empty or len(df.columns) == 0:
                continue
            
            # 清理工作表名称作为类别
            category = sheet_name.replace('表1_', '').replace('表2_', '').replace('表3_', '')
            category = re.sub(r'^表\d+_', '', category)
            
            names = []
            first_column = df.iloc[:, 0]
            
            for value in first_column:
                if pd.notna(value) and str(value).strip():
                    name = str(value).strip()
                    name = re.sub(r'^\d+\.?\s*', '', name)
                    name = re.sub(r'\s+', ' ', name)
                    
                    if len(name) > 2:
                        names.append(name)
            
            if names:
                categories[category] = names
                print(f"📋 {category}: {len(names)} 个名称")
    
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return {}
    
    return categories

def concurrent_search_batch(searcher, names_batch, batch_id, max_workers=30):
    """并发搜索一批名称"""
    
    print(f"\n🚀 批次{batch_id}: 开始30并发搜索 {len(names_batch)} 个点位")
    
    results = []
    start_time = time.time()
    
    # 真正的30并发执行
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务，每个任务分配一个线程ID
        future_to_info = {
            executor.submit(searcher.search_single_coordinate, name, (i % max_workers) + 1): (i, name)
            for i, name in enumerate(names_batch)
        }
        
        # 收集结果
        completed = 0
        for future in as_completed(future_to_info):
            result = future.result()
            results.append(result)
            completed += 1
            
            # 显示进度
            if completed % 10 == 0 or completed == len(names_batch):
                elapsed = time.time() - start_time
                rate = completed / elapsed if elapsed > 0 else 0
                print(f"📊 批次{batch_id}进度: {completed}/{len(names_batch)} ({completed/len(names_batch)*100:.1f}%) - {rate:.1f}/秒")
    
    elapsed = time.time() - start_time
    print(f"⏱️ 批次{batch_id}完成，耗时: {elapsed:.1f}秒")
    
    return results

def save_results_by_category(all_results, categories, output_file):
    """按类别保存结果到不同的工作表"""
    
    # 创建名称到类别的映射
    name_to_category = {}
    for category, names in categories.items():
        for name in names:
            name_to_category[name] = category
    
    # 按类别分组结果
    results_by_category = defaultdict(list)
    for result in all_results:
        name = result['名称']
        category = name_to_category.get(name, '未分类')
        results_by_category[category].append(result)
    
    # 保存到Excel的不同工作表
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        
        # 总览表
        df_all = pd.DataFrame(all_results)
        df_all.to_excel(writer, sheet_name='总览', index=False)
        print(f"💾 保存总览表: {len(df_all)} 条记录")
        
        # 各类别分表
        for category, results in results_by_category.items():
            if results:
                df_category = pd.DataFrame(results)
                
                # 清理工作表名称
                sheet_name = category[:31]  # Excel限制
                sheet_name = re.sub(r'[\\/*?:"<>|]', '_', sheet_name)
                
                df_category.to_excel(writer, sheet_name=sheet_name, index=False)
                print(f"💾 保存{category}表: {len(df_category)} 条记录")

def main():
    """主函数"""
    excel_file = "台湾隧道特定表格.xlsx"
    output_file = "台湾隧道坐标_真正30并发_分类表.xlsx"
    
    print("=== 台湾隧道坐标真正30并发搜索 ===")
    print(f"📁 输入文件: {excel_file}")
    print(f"💾 输出文件: {output_file}")
    print(f"⚡ 并发数: 30个线程同时搜索")
    
    # 1. 按类别提取名称
    print("\n步骤1: 按类别提取名称...")
    categories = extract_names_by_category(excel_file)
    
    if not categories:
        print("❌ 未找到任何有效名称")
        return
    
    # 统计总数
    total_names = sum(len(names) for names in categories.values())
    print(f"✅ 共提取到 {len(categories)} 个类别，{total_names} 个名称")
    
    # 2. 合并所有名称进行搜索
    all_names = []
    for category, names in categories.items():
        all_names.extend(names)
    
    # 去重
    unique_names = list(dict.fromkeys(all_names))  # 保持顺序的去重
    print(f"📊 去重后: {len(unique_names)} 个唯一名称")
    
    # 3. 分批进行30并发搜索
    searcher = CoordinateSearcher()
    all_results = []
    batch_size = 100  # 每批100个名称
    
    print(f"\n步骤2: 开始真正30并发搜索...")
    start_time = time.time()
    
    for i in range(0, len(unique_names), batch_size):
        batch = unique_names[i:i+batch_size]
        batch_id = i // batch_size + 1
        
        batch_results = concurrent_search_batch(searcher, batch, batch_id, max_workers=30)
        all_results.extend(batch_results)
        
        # 批次间短暂休息
        if i + batch_size < len(unique_names):
            print("😴 批次间休息2秒...")
            time.sleep(2)
    
    # 4. 保存分类结果
    print(f"\n步骤3: 保存分类结果...")
    save_results_by_category(all_results, categories, output_file)
    
    # 5. 最终统计
    total_time = time.time() - start_time
    success_count = searcher.stats['successful']
    failed_count = searcher.stats['failed']
    
    print(f"\n" + "="*60)
    print(f"🎉 搜索完成！")
    print(f"⏱️  总耗时: {total_time:.1f} 秒")
    print(f"📊 总计: {len(all_results)} 个名称")
    print(f"✅ 成功: {success_count} 个 ({success_count/len(all_results)*100:.1f}%)")
    print(f"❌ 失败: {failed_count} 个")
    print(f"🌐 API请求: {searcher.stats['total_requests']} 次")
    print(f"🚀 平均速度: {len(all_results)/total_time:.1f} 个/秒")
    print(f"💾 结果已保存到: {output_file}")
    print(f"📋 包含 {len(categories)} 个分类表 + 1个总览表")

if __name__ == "__main__":
    main()
